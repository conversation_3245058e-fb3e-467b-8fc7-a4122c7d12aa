// admin.types.ts
export interface FilterOption {
  label: string;
  value: string;
}

export interface FilterState {
  attributeFilters: {
    attribute: FilterOption | null;
    storeId: string;
  };
  userFilters: {
    active: FilterOption | null;
    userType: FilterOption | null;
  };
}

export interface AttributeItem {
  RecID?: number;
  ListName: string;
  ItemName: string;
  ItemValue: string;
  ItemDescription?: string;
  ItemOrder?: number;
  ParentListName?: string;
  ParentItemValue?: string;
}

export interface Role {
  RoleID: string;
  RoleName: string;
  RoleDescription?: string;
}

export interface Manager {
  UserID: string;
  UserLogin: string;
  UserFullName?: string;
}

export interface User {
  UserID?: string;
  UserLogin: string;
  UserFirstName?: string;
  UserLastName?: string;
  UserFullName?: string;
  UserEmail?: string;
  MarsUsername?: string;
  UserType?: string;
  ManagerID?: string;
  IsLockedOut?: boolean;
  Active?: boolean;
  RoleID?: string;
  RoleName?: string;
  roles?: {
    RoleName: string;
  }[];
}

export interface AdminState {
  filters: FilterState;
  currentAttribute: AttributeItem | null;
  currentUser: User | null;
  attributes: AttributeItem[];
  users: User[];
  roles: Role[];
  managers: Manager[];
  isLoading: boolean;
  error: string | null;
  listNames: string[];
  totalCountAttributes: number;
  totalCountUsers: number;
}
