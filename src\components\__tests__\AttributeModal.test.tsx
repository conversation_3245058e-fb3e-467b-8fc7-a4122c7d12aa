import { describe, it, expect, vi, afterEach, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import AttributeModal from '../AttributeModal';
import React from 'react';

describe('AttributeModal', () => {
  const mockOnClose = vi.fn();
  const mockOnSave = vi.fn();

  const initialData = {
    ListName: 'TestList',
    ItemName: 'TestItem',
    ItemValue: 'test-value',
    ItemOrder: 1,
    ItemDescription: 'Test description',
    ParentListName: 'ParentList',
    ParentItemValue: 'parent-value'
  };

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Add Mode', () => {
    beforeEach(() => {
      render(
        <AttributeModal
          open={true}
          onClose={mockOnClose}
          onSave={mockOnSave}
          mode="add"
        />
      );
    });

    it('should render the modal with add title', () => {
      expect(screen.getByText('Add Attribute')).toBeInTheDocument();
    });

    it('should update form fields when user types', () => {
      const listNameInput = screen.getAllByRole('textbox')[0];
      fireEvent.change(listNameInput, { target: { value: 'NewList' } });
      expect(listNameInput).toHaveValue('NewList');
    });

    it('should call onSave with form data when save button is clicked', () => {
      const inputs = screen.getAllByRole('textbox');
      fireEvent.change(inputs[0], { target: { value: 'NewList' } });
      fireEvent.change(inputs[1], { target: { value: 'NewItem' } });
      fireEvent.change(inputs[2], { target: { value: 'new-value' } });
      const itemOrderInput = screen.getByRole('spinbutton');
      fireEvent.change(itemOrderInput, { target: { value: '2' } });

      fireEvent.click(screen.getByText('Save'));

      expect(mockOnSave).toHaveBeenCalledWith({
        ListName: 'NewList',
        ItemName: 'NewItem',
        ItemValue: 'new-value',
        ItemOrder: 2,
        ItemDescription: undefined,
        ParentListName: undefined,
        ParentItemValue: undefined,
      });
    });

    it('should call onClose when cancel button is clicked', () => {
      fireEvent.click(screen.getByText('Cancel'));
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Edit Mode', () => {
    beforeEach(() => {
      render(
        <AttributeModal
          open={true}
          onClose={mockOnClose}
          onSave={mockOnSave}
          mode="edit"
          initialData={initialData}
        />
      );
    });

    it('should render the modal with edit title', () => {
      expect(screen.getByText('Edit Attribute')).toBeInTheDocument();
    });

    it('should populate form fields with initial data', () => {
      const inputs = screen.getAllByRole('textbox');
      expect(inputs[0]).toHaveValue(initialData.ListName);
      expect(inputs[1]).toHaveValue(initialData.ItemName);
      expect(inputs[2]).toHaveValue(initialData.ItemValue);
      expect(screen.getByRole('spinbutton')).toHaveValue(initialData.ItemOrder);
      expect(inputs[3]).toHaveValue(initialData.ParentListName);
      expect(inputs[4]).toHaveValue(initialData.ParentItemValue);
      expect(inputs[5]).toHaveValue(initialData.ItemDescription);
    });

    it('should call onSave with updated data when save button is clicked', () => {
      const inputs = screen.getAllByRole('textbox');
      fireEvent.change(inputs[1], { target: { value: 'UpdatedItem' } });
      fireEvent.click(screen.getByText('Save'));

      expect(mockOnSave).toHaveBeenCalledWith({
        ...initialData,
        ItemName: 'UpdatedItem',
        ItemOrder: initialData.ItemOrder,
      });
    });

    it('should handle empty ItemOrder by sending undefined', () => {
      const itemOrderInput = screen.getByRole('spinbutton');
      fireEvent.change(itemOrderInput, { target: { value: '' } });
      fireEvent.click(screen.getByText('Save'));

      expect(mockOnSave).toHaveBeenCalledWith(expect.objectContaining({
        ItemOrder: undefined
      }));
    });
  });

  describe('Form Validation', () => {
    it('should require ListName, ItemName, and ItemValue', () => {
      render(
        <AttributeModal
          open={true}
          onClose={mockOnClose}
          onSave={mockOnSave}
          mode="add"
        />
      );

      fireEvent.click(screen.getByText('Save'));

      expect(mockOnSave).toHaveBeenCalledWith(expect.objectContaining({
        ListName: '',
        ItemName: '',
        ItemValue: ''
      }));
    });
  });
});
