import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../store";
import {
  Modal,
  <PERSON>pography,
  Button,
  TextField,
  Grid,
  Box,
  Select,
  MenuItem,
  InputLabel,
  FormControl,
  Checkbox,
  FormControlLabel,
  Divider,
} from "@mui/material";
import { toast } from "react-toastify";

interface UserModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (user: any) => void;
  mode: "add" | "edit";
  initialData?: any;
}

const style = {
  position: "absolute" as const,
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 800,
  maxWidth: "95%",
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: "24px 16px",
  maxHeight: "90vh",
  overflowY: "auto",
};

const userTypes = [
  { value: "Customer", label: "Customer" },
  { value: "NPDDC", label: "NPDDC" },
  { value: "Partner", label: "Partner" },
];

const UserModal: React.FC<UserModalProps> = ({ open, onClose, onSave, mode, initialData }) => {
  const { roles, managers } = useSelector((state: RootState) => state.admin);
  const [formData, setFormData] = useState({
    UserLogin: "",
    UserFirstName: "",
    UserLastName: "",
    UserEmail: "",
    MarsUsername: "",
    UserType: "",
    ManagerID: "",
    IsLockedOut: false,
    Active: true,
    RoleID: "",
  });

  const [errors, setErrors] = useState({
    UserLogin: false,
    UserFirstName: false,
    UserLastName: false,
    UserEmail: false,
    UserType: false,
    RoleID: false,
    MarsUsername: false,
  });

  useEffect(() => {
    if (mode === "edit" && initialData) {
      const userRole = initialData.roles?.[0] || {};

      setFormData({
        UserLogin: initialData.UserLogin || "",
        UserFirstName: initialData.UserFirstName || "",
        UserLastName: initialData.UserLastName || "",
        UserEmail: initialData.UserEmail || "",
        MarsUsername: initialData.MarsUsername || "",
        UserType: initialData.UserType || "",
        ManagerID: initialData.ManagerID || "",
        IsLockedOut: initialData.IsLockedOut || false,
        Active: initialData.Active !== false,
        RoleID: userRole.RoleID || "",
      });
    } else {
      setFormData({
        UserLogin: "",
        UserFirstName: "",
        UserLastName: "",
        UserEmail: "",
        MarsUsername: "",
        UserType: "",
        ManagerID: "",
        IsLockedOut: false,
        Active: true,
        RoleID: "",
      });
    }
    // Reset errors when opening modal
    setErrors({
      UserLogin: false,
      UserFirstName: false,
      UserLastName: false,
      UserEmail: false,
      UserType: false,
      RoleID: false,
      MarsUsername: false,
    });
  }, [mode, initialData, open]);

  const validateForm = () => {
    const newErrors = {
      UserLogin: !formData.UserLogin,
      UserFirstName: !formData.UserFirstName,
      UserLastName: !formData.UserLastName,
      UserEmail: !formData.UserEmail,
      UserType: !formData.UserType,
      RoleID: !formData.RoleID,
      MarsUsername: formData.MarsUsername.length > 8,
    };
    setErrors(newErrors);
    return !Object.values(newErrors).some(Boolean);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: false }));
    }
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user selects an option
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: false }));
    }
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      if (formData.MarsUsername.length > 8) {
        toast.error("Mars Username must be 8 characters or less");
      } else {
        toast.error("Please fill in all required fields");
      }
      return;
    }
    onSave(formData);
    onClose();
  };

  const renderTextField = (
    label: string,
    name: keyof typeof formData,
    type = "text",
    required = false,
  ) => (
    <Grid container alignItems="center" spacing={2} sx={{ mb: 2 }}>
      <Grid item xs={4} sx={{ textAlign: "right", pr: 1.5 }}>
        <Typography sx={{ fontSize: 12, fontWeight: 600 }}>
          {label} {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={8}>
        <TextField
          name={name}
          data-testid={`${name}-text`}
          value={formData[name]}
          onChange={handleChange}
          variant="outlined"
          fullWidth
          size="small"
          type={type}
          error={errors[name as keyof typeof errors]}
          helperText={
            errors[name as keyof typeof errors]
              ? name === "MarsUsername"
                ? "Mars Username must be 8 characters or less"
                : "This field is required"
              : ""
          }
          inputProps={name === "MarsUsername" ? { maxLength: 8 } : {}}
          sx={{
            "& .MuiInputBase-input": {
              fontSize: 14,
              padding: "10px",
            },
          }}
        />
      </Grid>
    </Grid>
  );

  const renderSelectField = (
    label: string,
    name: keyof typeof formData,
    options: Array<{ value: string; label: string }>,
    required = false,
  ) => (
    <Grid container alignItems="center" spacing={2} sx={{ mb: 2 }}>
      <Grid item xs={4} sx={{ textAlign: "right", pr: 2 }}>
        <Typography sx={{ fontSize: 12, fontWeight: 600 }}>
          {label} {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>

      <Grid item xs={8}>
        <FormControl fullWidth size="small" error={errors[name as keyof typeof errors]}>
          <Select
            name={name}
            data-testid={`${name}-select`}
            value={formData[name]}
            onChange={handleSelectChange}
            sx={{
              fontSize: 14,
              "& .MuiSelect-select": {
                padding: "10px",
              },
            }}
          >
            <MenuItem value="">
              <em>Select {label}</em>
            </MenuItem>
            {options.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
          {errors[name as keyof typeof errors] && (
            <Typography variant="caption" color="error" sx={{ display: "block", mt: 0.5 }}>
              This field is required
            </Typography>
          )}
        </FormControl>
      </Grid>
    </Grid>
  );

  const renderCheckboxField = (label: string, name: keyof typeof formData) => (
    <Grid container alignItems="center" spacing={2} sx={{ mb: 2 }}>
      <Grid item xs={4} sx={{ textAlign: "right", pr: 2 }}>
        <Typography sx={{ fontSize: 12, fontWeight: 600 }}>{label}</Typography>
      </Grid>
      <Grid item xs={8}>
        <FormControlLabel
          control={
            <Checkbox
              name={name}
              data-testid={`${name}-checkbox`}
              checked={formData[name] as boolean}
              onChange={handleChange}
              color="primary"
            />
          }
          label=""
        />
      </Grid>
    </Grid>
  );

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={style}>
        <Typography
          variant="h6"
          sx={{
            fontFamily: '"Mars Centra", sans-serif',
            fontWeight: 700,
            fontSize: "12px",
            lineHeight: "100%",
            letterSpacing: "0%",
            textTransform: "uppercase",
            mb: 3,
            px: 1,
          }}
        >
          {mode === "add" ? "Add User" : "Edit User"}
        </Typography>

        <Box sx={{ px: 1, mb: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={6}>
              {renderTextField("Login/Username", "UserLogin", "text", true)}
              {renderTextField("First Name", "UserFirstName", "text", true)}
              {renderTextField("Last Name", "UserLastName", "text", true)}
              {renderTextField("Email", "UserEmail", "email", true)}
            </Grid>
            <Grid item xs={6}>
              {renderTextField("Mars Username", "MarsUsername")}
              {renderSelectField("User Type", "UserType", userTypes, true)}
              {renderSelectField(
                "Role",
                "RoleID",
                roles.map((role) => ({
                  value: role.RoleID,
                  label: role.RoleName,
                })),
                true,
              )}
              {renderSelectField(
                "Manager",
                "ManagerID",
                managers.map((manager) => ({
                  value: manager.UserID,
                  label: manager.UserFullName || manager.UserLogin,
                })),
              )}
            </Grid>
          </Grid>

          {/* Single row for checkboxes */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 4,
              mt: 3,
              alignItems: "center",
            }}
          >
            <FormControlLabel
              control={
                <Checkbox
                  name="IsLockedOut"
                  checked={formData.IsLockedOut}
                  onChange={handleChange}
                  color="primary"
                />
              }
              label="Locked Out"
              sx={{
                "& .MuiFormControlLabel-label": {
                  fontSize: 12,
                  fontWeight: 600,
                },
              }}
            />
            <FormControlLabel
              control={
                <Checkbox
                  name="Active"
                  checked={formData.Active}
                  onChange={handleChange}
                  color="primary"
                />
              }
              label="Active"
              sx={{
                "& .MuiFormControlLabel-label": {
                  fontSize: 12,
                  fontWeight: 600,
                },
              }}
            />
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-start",
            gap: 2,
            px: 1,
            pt: 1,
          }}
        >
          <Button
            variant="contained"
            onClick={handleSubmit}
            sx={{
              backgroundColor: "#00008B",
              color: "#fff",
              fontWeight: 600,
              px: 4,
              borderRadius: 1,
              "&:hover": { backgroundColor: "#000070" },
            }}
          >
            Save
          </Button>
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              color: "#00008B",
              borderColor: "#00008B",
              fontWeight: 600,
              px: 4,
              borderRadius: 1,
              "&:hover": {
                backgroundColor: "#f0f0f0",
                borderColor: "#00008B",
              },
            }}
          >
            Cancel
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default UserModal;
