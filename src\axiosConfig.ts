import { ReactElement, useEffect } from 'react';
// import qs from 'qs';
import axios, {
  AxiosError,
  AxiosHeaders,
  AxiosInstance,
  AxiosResponse,
  CreateAxiosDefaults,
  InternalAxiosRequestConfig,
} from 'axios';

// const apiUrl = 'http://127.0.0.1:8000/api/v1/';
const apiUrl = import.meta.env.VITE_API_URL;


const options: CreateAxiosDefaults = {
  baseURL: apiUrl,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  // paramsSerializer: {
  //   serialize: (params: any) => qs.stringify(params, { arrayFormat: 'repeat' }),
  // },
};

const axiosInstance: AxiosInstance = axios.create(options);

// Retry helper function
const retryRequest = (
  error: AxiosError,
  retriesLeft: number,
  delay: number
): Promise<AxiosResponse | void> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (retriesLeft === 0 || !error.config) {
        reject(error);
      } else {
        console.log(`Retrying request... (${10 - retriesLeft + 1})`);
        axiosInstance.request(error.config as InternalAxiosRequestConfig)
          .then(resolve)
          .catch((err) => retryRequest(err, retriesLeft - 1, delay).then(resolve).catch(reject));
      }
    }, delay);
  });
};


const AxiosInterceptor = ({ children }: { children: ReactElement }) => {
  useEffect(() => {
    const onRequest = (
      config: InternalAxiosRequestConfig
    ): InternalAxiosRequestConfig => {
      config.headers = config.headers ?? AxiosHeaders.from({});
      config.headers.Authorization =
        `Bearer ${JSON.parse(localStorage.getItem('loginResponse') || '{}')?.access
        }` || '';
      return config;
    };

    const onRequestError = (error: AxiosError): Promise<AxiosError> => {
      const message =
        (error.response && error.response.data) || 'Something went wrong!';
      return Promise.reject(message);
    };

    const onResponse = (response: AxiosResponse): AxiosResponse =>
      response;

    const onResponseError = (error: AxiosError): Promise<any> => {
      console.error(`[response error] [${JSON.stringify(error)}]`);
      const message = (error.response && error.message) || 'Something went wrong!';
      if (!error.response) {
        // Retry logic if no response received
        return retryRequest(error, 10, 5000);
      }
      return Promise.reject(
        (error.response && error.response.data) || 'Something went wrong!'
      );
    };

    const requestInterceptor = axiosInstance.interceptors.request.use(
      onRequest,
      onRequestError
    );
    const responseInterceptor = axiosInstance.interceptors.response.use(
      onResponse,
      onResponseError
    );

    return () => {
      axiosInstance.interceptors.request.eject(requestInterceptor);
      axiosInstance.interceptors.response.eject(responseInterceptor);
    };
  }, []);

  return children;
};

export default axiosInstance;
export { AxiosInterceptor };
