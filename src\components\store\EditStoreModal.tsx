import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>pography,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Stack,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { updateStoreMappingMetadata } from "../../services/action/store.action";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAppDispatch, useAppSelector } from "../../services/hooks/store.hooks";
import { StoreInfo } from "../../types/store.types";

const style = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: { xs: "90%", sm: "80%", md: "60%" },
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
  borderRadius: 2,
  maxHeight: "90vh",
  overflowY: "auto",
};

interface EditStoreModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: (updatedInfo: Partial<StoreInfo>) => void;
  storeInfo: {
    storeId: string;
    description: string;
    dataSource: string;
    address: string;
    firstSeen: string;
    lastUpdated: string;
    status: string;
    phone: string;
    rep: string;
    customerType: string;
    warehouse: string;
    country: string;
    last4WeekPOS: string;
    last52WeekPOS: string;
    total2010On: string;
    isPending: boolean;
  };
}

const EditStoreModal: React.FC<EditStoreModalProps> = ({ open, onClose, storeInfo, onSuccess }) => {
  const dispatch = useDispatch();
  const appDispatch = useAppDispatch();
  const [formData, setFormData] = useState({
    customer_type: storeInfo.customerType || "-",
    rep: storeInfo.rep || "-",
    phone: storeInfo.phone || "-",
    country: storeInfo.country || "-",
    warehouse: storeInfo.warehouse || "-",
    address: storeInfo.address || "-",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Dispatch the update action and wait for it to complete
      const result = await appDispatch(
        updateStoreMappingMetadata({
          VCID: parseInt(storeInfo.storeId),
          ...Object.fromEntries(
            Object.entries(formData).map(([key, value]) => [key, value === "-" ? "" : value]),
          ),
        }),
      ).unwrap();

      toast.success("Store information updated successfully!");

      // Call the success callback with updated info if provided
      if (onSuccess) {
        onSuccess({
          customerType: formData.customer_type,
          rep: formData.rep,
          phone: formData.phone,
          country: formData.country,
          warehouse: formData.warehouse,
          address: formData.address,
        });
      }

      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to update store information");
    } finally {
      setIsLoading(false);
    }
  };

  const displayValue = (value: any) => {
    return value?.trim() ? value : "-";
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="edit-store-modal-title"
      aria-describedby="edit-store-modal-description"
    >
      <Box sx={style}>
        <Typography id="edit-store-modal-title" variant="h6" component="h2" mb={3}>
          Edit Store Information
        </Typography>

        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Store ID
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.storeId)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Description
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.description)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Data Source
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.dataSource)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Status
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(
                  `${storeInfo.status ? "Mapped" : "Not Mapped"}${storeInfo.isPending ? " (Pending)" : ""}`,
                )}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                First Transaction
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.firstSeen)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Last Transaction
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.lastUpdated)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Customer Type"
                name="customer_type"
                value={formData.customer_type}
                onChange={handleChange}
                variant="outlined"
                margin="normal"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Representative"
                name="rep"
                value={formData.rep}
                onChange={handleChange}
                variant="outlined"
                margin="normal"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Last 4 Week POS
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.last4WeekPOS)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Last 52 Week POS
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.last52WeekPOS)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                variant="outlined"
                margin="normal"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Country"
                name="country"
                value={formData.country}
                onChange={handleChange}
                variant="outlined"
                margin="normal"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" color="textSecondary">
                Total POS (2010 On)
              </Typography>
              <Typography variant="body1" mb={2}>
                {displayValue(storeInfo.total2010On)}
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Warehouse"
                name="warehouse"
                value={formData.warehouse}
                onChange={handleChange}
                variant="outlined"
                margin="normal"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                variant="outlined"
                margin="normal"
                multiline
                rows={3}
              />
            </Grid>
          </Grid>

          <Stack direction="row" spacing={2} justifyContent="flex-end" mt={3}>
            <Button variant="outlined" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" variant="contained" color="primary" disabled={isLoading}>
              {isLoading ? <CircularProgress size={24} /> : "Save Changes"}
            </Button>
          </Stack>
        </form>
      </Box>
    </Modal>
  );
};

export default EditStoreModal;
