.modalContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 800px; /* Increased width for horizontal layout */
  background-color: white;
  padding: 32px;
  border-radius: 8px;
  outline: none;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.15);
}

.modalTitle {
  margin-bottom: 24px;
  font-weight: 600;
  color: #333;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  gap: 16px;
}