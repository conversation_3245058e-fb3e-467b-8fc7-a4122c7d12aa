import React, { useState, useEffect, useCallback } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box, debounce } from "@mui/material";
import FilterRow from "../../components/FilterRow";
import { useNavigate } from "react-router-dom";
import { setSearchQuery, setStoreFilters, resetStoreFilters } from "../../store/slice/storeSlice";
import { searchUnmappedStores } from "../../services/action/store.action";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { toast } from "react-toastify";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

interface UnmappedStoreItem {
  DataSource: string;
  StoreID: string;
  StoreDesc: string;
  CityStateZip: string;
  FirstDt: string;
  LastDt: string;
  Status: string;
  VCID: number;
}

const UnmappedStoreSearch = () => {
  const dispatch: AppDispatch = useDispatch();
  const navigate = useNavigate();
  const { unmappedStores, isLoading, filters, generic_search, unmappedStoresTotalCount } =
    useSelector((state: RootState) => state.store);

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  // Show toast when no results are found
  useEffect(() => {
    if (
      !isLoading &&
      unmappedStores.length === 0 &&
      (generic_search || Object.values(filters).some(Boolean))
    ) {
      toast.info("No results found for your search criteria");
    }
  }, [unmappedStores, isLoading, generic_search, filters]);

  // Initial load
  useEffect(() => {
    dispatch(
      searchUnmappedStores({
        limit: pagination.rowsPerPage,
        page: pagination.page,
      }),
    );
  }, [dispatch, pagination.page, pagination.rowsPerPage]);

  const fetchStores = useCallback(() => {
    dispatch(
      searchUnmappedStores({
        limit: pagination.rowsPerPage,
        page: pagination.page,
        generic_search,
        ...filters,
      }),
    );
  }, [dispatch, filters, generic_search, pagination.page, pagination.rowsPerPage]);

  // Immediate API call on generic search change
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (generic_search !== undefined) {
        fetchStores();
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [generic_search]);

  useEffect(() => {
    fetchStores();
  }, [pagination.page, pagination.rowsPerPage]);

  const handleSearch = useCallback(
    debounce((query: string) => {
      dispatch(setSearchQuery(query));
      setPagination((prev) => ({ ...prev, page: 1 }));
    }, 500),
    [dispatch],
  );

  const handleFilterChange = (name: string, value: string) => {
    dispatch(setStoreFilters({ [name]: value }));
  };

  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchStores();
  };

  const handleResetFilters = () => {
    dispatch(resetStoreFilters());
    dispatch(setSearchQuery(""));
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchStores();
  };

  const handleStoreDescClick = (row: UnmappedStoreItem) => {
    if (!row?.VCID) return;
    navigate(`/store-desc/${row.VCID}`, { state: { distStoreId: row.StoreID } });
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const columns = [
    {
      id: "DataSource",
      label: "Data Source Name",
    },
    {
      id: "StoreID",
      label: "Store ID",
    },
    {
      id: "StoreDesc",
      label: "Store Desc",
      format: (value: string, row: UnmappedStoreItem) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            textDecoration: "underline",
            cursor: "pointer",
            "&:hover": {
              color: "primary.dark",
              fontWeight: "500",
            },
          }}
          onClick={() => handleStoreDescClick(row)}
        >
          {value}
        </Box>
      ),
    },
    {
      id: "CityStateZip",
      label: "City/State/ZIP",
    },
    {
      id: "FirstDt",
      label: "First DT",
    },
    {
      id: "LastDt",
      label: "Last DT",
    },
    {
      id: "Status",
      label: "Status",
      format: (value: string) => (
        <Box
          sx={{
            color: value === "Active" ? "success.main" : "error.main",
            fontWeight: "bold",
          }}
        >
          {value}
        </Box>
      ),
    },
  ];

  const filterConfigs = [
    {
      name: "DataSource",
      label: "Data Source",
      type: "text" as const,
      value: filters.DataSource || "",
      onChange: (value: string) => handleFilterChange("DataSource", value),
    },
    {
      name: "DistStoreID",
      label: "Store ID",
      type: "text" as const,
      value: filters.DistStoreID || "",
      onChange: (value: string) => handleFilterChange("DistStoreID", value),
    },
    {
      name: "DistStoreName",
      label: "Store Name",
      type: "text" as const,
      value: filters.DistStoreName || "",
      onChange: (value: string) => handleFilterChange("DistStoreName", value),
    },
    {
      name: "DistStoreAddress",
      label: "Store Address",
      type: "text" as const,
      value: filters.DistStoreAddress || "",
      onChange: (value: string) => handleFilterChange("DistStoreAddress", value),
    },
    {
      name: "DistStoreCity",
      label: "Store City",
      type: "text" as const,
      value: filters.DistStoreCity || "",
      onChange: (value: string) => handleFilterChange("DistStoreCity", value),
    },
    {
      name: "DistStoreState",
      label: "Store State",
      type: "text" as const,
      value: filters.DistStoreState || "",
      onChange: (value: string) => handleFilterChange("DistStoreState", value),
    },
    {
      name: "DistStoreZip",
      label: "Store ZIP",
      type: "text" as const,
      value: filters.DistStoreZip || "",
      onChange: (value: string) => handleFilterChange("DistStoreZip", value),
    },
    {
      name: "DistStorePhone",
      label: "Store Phone",
      type: "text" as const,
      value: filters.DistStorePhone || "",
      onChange: (value: string) => handleFilterChange("DistStorePhone", value),
    },
  ];

  return (
    <div>
      <TopBarLayout
        breadcrumbItems={["Admin", "Unmapped Store Search"]}
        onSearchChange={handleSearch}
      />

      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {!isLoading && unmappedStores.length === 0 ? (
        <Box sx={{ p: 3, textAlign: "center" }}>No stores found matching your search criteria</Box>
      ) : (
        <TableComponent
          columns={columns}
          rows={unmappedStores}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={unmappedStoresTotalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default UnmappedStoreSearch;
