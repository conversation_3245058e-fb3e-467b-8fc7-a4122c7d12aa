import React from 'react';
import { render, screen } from '@testing-library/react';
import Breadcrumb from '../Breadcrump'; // update the path as needed

describe('Breadcrumb component', () => {
  const items = ['Home', 'Products', 'Stores', 'Admin'];

  it('renders all breadcrumb items', () => {
    render(<Breadcrumb items={items} />);

    items.forEach((item) => {
      expect(screen.getByText(item)).toBeInTheDocument();
    });
  });

  it('renders the correct number of breadcrumb links', () => {
    render(<Breadcrumb items={items} />);
    const links = screen.getAllByRole('link');
    expect(links.length).toBe(items.length);
  });

  it('applies the "last" style only to the last item', () => {
    render(<Breadcrumb items={items} />);
    const lastLink = screen.getByText('Admin');
    expect(lastLink.className).toContain('last');

    // Ensure other links do NOT have 'last' class
    items.slice(0, -1).forEach((item) => {
      const link = screen.getByText(item);
      expect(link.className).not.toContain('last');
    });
  });

  it('uses ">" as the separator', () => {
    render(<Breadcrumb items={['A', 'B']} />);
    expect(screen.getByText('>')).toBeInTheDocument();
  });
});
