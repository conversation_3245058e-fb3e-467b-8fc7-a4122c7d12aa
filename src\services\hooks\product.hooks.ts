import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchListItems, searchProducts, fetchProductById } from '../action/product.action';
import { clearSearchResults, clearSelectedProduct, clearErrors } from '../../store/slice/productSlice';
import { ProductSearchParams } from '../api/product.service';

export const useProduct = () => {
  const dispatch = useDispatch<AppDispatch>();
  
  const {
    listItems,
    listItemsLoading,
    listItemsError,
    products,
    searchLoading,
    searchError,
    totalCount,
    currentPage,
    totalPages,
    selectedProduct,
    productLoading,
    productError,
  } = useSelector((state: RootState) => state.product);

  // Fetch list items for dropdowns
  const getListItems = useCallback(() => {
    dispatch(fetchListItems());
  }, [dispatch]);

  // Search products with parameters
  const searchProductsWithParams = useCallback((params: ProductSearchParams) => {
    dispatch(searchProducts(params));
  }, [dispatch]);

  // Get product by ID
  const getProductById = useCallback((id: string) => {
    dispatch(fetchProductById(id));
  }, [dispatch]);

  // Clear search results
  const clearSearch = useCallback(() => {
    dispatch(clearSearchResults());
  }, [dispatch]);

  // Clear selected product
  const clearProduct = useCallback(() => {
    dispatch(clearSelectedProduct());
  }, [dispatch]);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    dispatch(clearErrors());
  }, [dispatch]);

  return {
    // State
    listItems,
    listItemsLoading,
    listItemsError,
    products,
    searchLoading,
    searchError,
    totalCount,
    currentPage,
    totalPages,
    selectedProduct,
    productLoading,
    productError,
    
    // Actions
    getListItems,
    searchProductsWithParams,
    getProductById,
    clearSearch,
    clearProduct,
    clearAllErrors,
  };
};
