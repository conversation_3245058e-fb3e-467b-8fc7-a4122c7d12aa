import { render, screen } from "@testing-library/react";
import { GlobalErrorBoundary } from "../GlobalErrorBoundary";
import { vi } from "vitest";

vi.mock("../GlobalErrorFallback", () => ({
  GlobalErrorFallback: () => <div>Global Error Fallback</div>,
}));

const ThrowError = () => {
  throw new Error("Test error");
};

describe("GlobalErrorBoundary", () => {
  it("should render children when no error is thrown", () => {
    render(
      <GlobalErrorBoundary>
        <div>Test</div>
      </GlobalErrorBoundary>,
    );
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("should render global fallback component when error is thrown", () => {
    render(
      <GlobalErrorBoundary>
        <ThrowError />
      </GlobalErrorBoundary>,
    );
    expect(screen.getByText("Global Error Fallback")).toBeInTheDocument();
  });
});
