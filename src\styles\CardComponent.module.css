.card-container {
  position: relative;
  display: flex;
  width: 300px;
  height: 150px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid transparent; /* Prevents margin collapse */
}

.card-container:hover {
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.info-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

/* Circle Container - Foolproof Implementation */
.logo-circle-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
}

/* Perfect Circle Background */
.logo-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 160, 0.04);
  border-radius: 50%;
  aspect-ratio: 1/1; /* Ensures perfect circle */
  box-shadow: inset 0 0 0 1px rgba(0, 0, 160, 0.1); /* Optional subtle border */
}

/* Logo with Perfect Centering */
.card-logo {
  position: absolute;
  width: 50px;
  height: 50px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  object-fit: contain;
  opacity: 0.9;
  transition: opacity 0.2s ease;
  padding: 2px; /* Optional subtle spacing */
}

.card-logo:hover {
  opacity: 1;
}

.card-title {
  font-family: 'Mars Centra', sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0.5px;
  text-align: center;
  margin-top: 4px;
  color: #333;
}

/* Arrow icon styling */
.arrow-icon {
  color: #666;
  transition: transform 0.2s ease;
  margin-bottom: 13px;
}

.card-container:hover .arrow-icon {
  transform: translateX(2px);
  color: #000;
}