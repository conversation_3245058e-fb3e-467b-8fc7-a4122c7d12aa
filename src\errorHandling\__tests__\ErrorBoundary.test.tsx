import { act, render, screen } from "@testing-library/react";
import { ErrorBoundary } from "../ErrorBoundary";
import { logError } from "../logError";
import { vi } from "vitest";

vi.mock("../logError");

const ThrowError = () => {
  throw new Error("Test error");
};

describe("ErrorBoundary", () => {
  it("should render children when no error is thrown", () => {
    render(
      <ErrorBoundary>
        <div>Test</div>
      </ErrorBoundary>,
    );
    expect(screen.getByText("Test")).toBeInTheDocument();
  });

  it("should render fallback component when error is thrown", () => {
    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>,
    );
    expect(screen.getByText("Something went wrong")).toBeInTheDocument();
  });

  it("should call logError when error is thrown", () => {
    render(
      <ErrorBoundary>
        <ThrowError />
      </ErrorBoundary>,
    );
    expect(logError).toHaveBeenCalled();
  });

  it("should use custom fallback component when provided", () => {
    const CustomFallback = ({ error }: { error: Error }) => (
      <div>Custom Fallback: {error.message}</div>
    );

    render(
      <ErrorBoundary FallbackComponent={CustomFallback}>
        <ThrowError />
      </ErrorBoundary>,
    );
    expect(screen.getByText("Custom Fallback: Test error")).toBeInTheDocument();
  });

  it("should call onReset when reset button is clicked", () => {
    const onReset = vi.fn();
    render(
      <ErrorBoundary onReset={onReset}>
        <ThrowError />
      </ErrorBoundary>,
    );

    act(() => {
      screen.getByText("Try again").click();
    });

    expect(onReset).toHaveBeenCalled();
  });
});
