import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import SearchBar from "../Search";

describe("SearchBar Component", () => {
  it("renders the search input field", () => {
    render(<SearchBar />);
    const input = screen.getByPlaceholderText("Search");
    expect(input).toBeInTheDocument();
  });

  it("displays the search icon", () => {
    render(<SearchBar />);
    const icon = screen.getByTestId("SearchIcon");
    expect(icon).toBeInTheDocument();
  });

  it("calls onSearchChange when input value changes", () => {
    const mockOnSearchChange = vi.fn();
    render(<SearchBar onSearchChange={mockOnSearchChange} />);
    const input = screen.getByPlaceholderText("Search");

    fireEvent.change(input, { target: { value: "test query" } });

    expect(mockOnSearchChange).toHaveBeenCalledWith("test query");
    expect(mockOnSearchChange).toHaveBeenCalledTimes(1);
  });
});
