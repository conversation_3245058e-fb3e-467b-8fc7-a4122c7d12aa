import { LocalizationProvider, ThemeProvider } from "@mars/vizx-react";
import { Routes } from "./routes/Routes";
import { QueryClientProvider } from "@tanstack/react-query";
// import { queryClient } from "./api/queryClient";
import store from "./store";
import { Provider } from "react-redux";
import "react-toastify/dist/ReactToastify.css";

export const App = () => {
  return (
    <Provider store={store}>
    <ThemeProvider>
      {/* <LocalizationProvider>
        <QueryClientProvider client={queryClient}> */}
          <Routes />
        {/* </QueryClientProvider>
      </LocalizationProvider> */}
    </ThemeProvider>
    </Provider>
  );
};
