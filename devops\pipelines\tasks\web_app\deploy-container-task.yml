parameters:
- name: azureSubscription
  type: string
- name: appName
  type: string
- name: container
  type: string
- name: appSettings
  type: string
- name: configurationStrings
  type: string

steps:
- task: AzureWebAppContainer@1
  displayName: Deployment
  inputs:
    azureSubscription: ${{parameters.azureSubscription}}
    appName: ${{parameters.appName}}
    containers: ${{parameters.container}}
    appSettings: ${{parameters.appSettings}}
    configurationStrings: ${{parameters.configurationStrings}}
