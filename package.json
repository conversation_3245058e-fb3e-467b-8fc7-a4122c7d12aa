{"name": "vizx-app-template", "private": true, "version": "1.0.0", "templateVersion": "1.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:prod": "tsc && vite build", "test": "vitest", "test:ci": "vitest run --coverage", "prettier:check": "npx prettier --check .", "prettier:fix": "npx prettier --write .", "preview": "vite preview", "analyze": "vite-bundle-visualizer -t sunburst"}, "dependencies": {"@azure/msal-browser": "^4.12.0", "@emotion/react": "^11.4.1", "@emotion/styled": "^11.3.0", "@mars/vizx-react": "^1.46.2", "@mui/icons-material": "^5.0.0", "@mui/material": "^5.0.0", "@mui/x-data-grid": "^6.18.7", "@reduxjs/toolkit": "^2.7.0", "@tanstack/react-query": "^5.66.7", "axios": "^1.9.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-redux": "^9.2.0", "react-router-dom": "^6.14.0", "react-toastify": "^11.0.5", "redux-mock-store": "^1.5.5", "xlsx": "^0.18.5"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/redux-mock-store": "^1.5.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8", "autoprefixer": "^10.4.14", "cors": "^2.8.5", "express": "^4.21.2", "globals": "^15.14.0", "jsdom": "^25.0.1", "msw": "^2.7.1", "postcss": "^8.4.24", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-svgr": "^4.3.0", "vitest": "^2.1.8"}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}"]}}