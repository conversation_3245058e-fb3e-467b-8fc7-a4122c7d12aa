import {
  FallbackProps as ReactFallbackProps,
  ErrorBoundary as ReactErrorBoundary,
} from "react-error-boundary";
import PropTypes from "prop-types";
import { logError } from "./logError";
import { ErrorFallback } from "@mars/vizx-react";

export type FallbackProps = ReactFallbackProps;

export type ErrorBoundaryProps = {
  children?: React.ReactNode;
  FallbackComponent?: React.ComponentType<FallbackProps>;
  onReset?: (...args: unknown[]) => void;
};

export const ErrorBoundary: React.FC<ErrorBoundaryProps> = ({
  children,
  FallbackComponent,
  onReset,
}) => {
  return (
    <ReactErrorBoundary
      FallbackComponent={FallbackComponent ?? ErrorFallback}
      onReset={onReset}
      onError={logError}
    >
      {children}
    </ReactErrorBoundary>
  );
};

ErrorBoundary.propTypes = {
  children: PropTypes.element,
  FallbackComponent: PropTypes.any,
  onReset: PropTypes.func,
};

ErrorBoundary.displayName = "ErrorBoundary";
