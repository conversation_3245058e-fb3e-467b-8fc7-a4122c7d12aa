import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FilterRow from '../FilterRow';

describe('FilterRow', () => {
  const mockOnChange = vi.fn();
  const mockOnReset = vi.fn();

  const filters = [
    {
      name: 'status',
      label: 'Status',
      options: [
        { value: 'all', label: 'All Status' },
        { value: 'active', label: 'Active' },
        { value: 'notActive', label: 'Not Active' },
      ],
      value: 'all',
      onChange: mockOnChange,
    },
    {
      name: 'role',
      label: 'Role',
      options: [
        { value: 'all', label: 'All Role' },
        { value: 'admin', label: 'Admin' },
        { value: 'user', label: 'User' },
      ],
      value: 'all',
      onChange: mockOnChange,
    },
  ];

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('displays the correct default selected option for each filter', () => {
    render(<FilterRow filters={filters} onReset={mockOnReset} />);
    expect(screen.getByText('All Status')).toBeInTheDocument();
    expect(screen.getByText('All Role')).toBeInTheDocument();
  });

  it('shows the label of the selected option after change', async () => {
    const { rerender } = render(<FilterRow filters={filters} onReset={mockOnReset} />);
    
    // Update props to simulate selection
    const updatedFilters = [
      { ...filters[0], value: 'active' },
      { ...filters[1], value: 'user' },
    ];
    rerender(<FilterRow filters={updatedFilters} onReset={mockOnReset} />);

    await waitFor(() => {
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('User')).toBeInTheDocument();
    });
  });

  it('should render all filters passed as props', () => {
    render(<FilterRow filters={filters} onReset={mockOnReset} />);
    expect(screen.getByText('All Status')).toBeInTheDocument();
    expect(screen.getByText('All Role')).toBeInTheDocument();
  });
});