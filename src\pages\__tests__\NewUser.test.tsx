import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter } from 'react-router-dom';
import NewUser from '../admin/NewUser';
import userEvent from '@testing-library/user-event';
import { toast } from 'react-toastify';

// Mock the Redux store and actions
const mockRoles = [
  { RoleID: '1', RoleName: 'Admin' },
  { RoleID: '2', RoleName: 'User' },
];
const mockManagers = [
  { UserID: '1', UserFullName: 'Manager One', UserLogin: 'manager1' },
  { UserID: '2', UserFullName: 'Manager Two', UserLogin: 'manager2' },
];

const mockStore = configureStore({
  reducer: {
    admin: () => ({
      roles: mockRoles,
      managers: mockManagers,
    }),
  },
});

// Mock the Redux actions
const mockCreateUser = vi.fn().mockResolvedValue({ UserID: '123' });
const mockAssignUserRole = vi.fn().mockResolvedValue({});
const mockGetAllRoles = vi.fn().mockResolvedValue({});
const mockGetAllManagers = vi.fn().mockResolvedValue({});

vi.mock('../../services/action/user.action', () => ({
  createUser: () => mockCreateUser,
  assignUserRole: () => mockAssignUserRole,
  getAllRoles: () => mockGetAllRoles,
  getAllManagers: () => mockGetAllManagers,
}));

// Mock toast
vi.mock('react-toastify', () => ({
  toast: {
    error: vi.fn(),
  },
}));

describe('NewUser Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <NewUser />
        </MemoryRouter>
      </Provider>
    );
  };

  it('should render the component with all form fields', () => {
    renderComponent();

    expect(screen.getByText('Fill New User Details')).toBeInTheDocument();
    expect(screen.getByTestId('UserLogin-input')).toBeInTheDocument();
    expect(screen.getByTestId('UserFirstName-input')).toBeInTheDocument();
    expect(screen.getByTestId('UserLastName-input')).toBeInTheDocument();
    expect(screen.getByTestId('UserEmail-input')).toBeInTheDocument();
    expect(screen.getByTestId('MarsUsername-input')).toBeInTheDocument();
    expect(screen.getByTestId('UserType-select')).toBeInTheDocument();
    expect(screen.getByTestId('RoleID-select')).toBeInTheDocument();
    expect(screen.getByTestId('ManagerID-select')).toBeInTheDocument();
    expect(screen.getByTestId('IsLockedOut-checkbox')).toBeInTheDocument();
    expect(screen.getByTestId('Active-checkbox')).toBeInTheDocument();
    expect(screen.getByTestId('submit-button')).toBeInTheDocument();
    expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
  });

  it('should update form data when text fields are changed', async () => {
    renderComponent();
    const user = userEvent.setup();

    const usernameInput = screen.getByTestId('UserLogin-input');
    await user.type(usernameInput, 'testuser');
    expect(usernameInput).toHaveValue('testuser');

    const firstNameInput = screen.getByTestId('UserFirstName-input');
    await user.type(firstNameInput, 'John');
    expect(firstNameInput).toHaveValue('John');
  });

  it('should update form data when select fields are changed', async () => {
    renderComponent();

    const userTypeSelect = screen.getByTestId('UserType-select');
    fireEvent.change(userTypeSelect, { target: { value: 'Customer' } });
    expect(userTypeSelect).toHaveValue('Customer');

    const roleSelect = screen.getByTestId('RoleID-select');
    fireEvent.change(roleSelect, { target: { value: '1' } });
    expect(roleSelect).toHaveValue('1');
  });


  it('should fetch roles and managers on mount', async () => {
    renderComponent();

    await waitFor(() => {
      expect(mockGetAllRoles).toHaveBeenCalled();
      expect(mockGetAllManagers).toHaveBeenCalled();
    });
  });

  it('should not assign role if no role is selected', async () => {
    renderComponent();
    const user = userEvent.setup();

    await user.type(screen.getByTestId('UserLogin-input'), 'testuser');
    await user.click(screen.getByTestId('submit-button'));

    await waitFor(() => {
      expect(mockCreateUser).toHaveBeenCalled();
      expect(mockAssignUserRole).not.toHaveBeenCalled();
    });
  });
});