import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  CircularProgress,
} from "@mui/material";
import TableComponent from "../TableComponent";
import { getChangeLog } from "../../services/action/store.action";
import { useAppDispatch, useAppSelector } from "../../services/hooks/store.hooks";

interface ChangeLogModalProps {
  nutroStoreID: number;
  open: boolean;
  onClose: () => void;
}

const ChangeLogModal: React.FC<ChangeLogModalProps> = ({ nutroStoreID, open, onClose }) => {
  const dispatch = useDispatch();
  const appDispatch = useAppDispatch();
  const { changeLog, isLoading, changeLogTotalCount } = useSelector(
    (state: RootState) => state.store,
  );

  const [dateFilter, setDateFilter] = useState("");
  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  useEffect(() => {
    if (open && nutroStoreID) {
      appDispatch(
        getChangeLog({
          nutrostore_id: nutroStoreID,
          date: dateFilter,
          page: pagination.page,
          limit: pagination.rowsPerPage,
        }),
      );
    }
  }, [dispatch, nutroStoreID, open, dateFilter, pagination.page, pagination.rowsPerPage]);

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const handleDateFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDateFilter(e.target.value);
  };

  const handleDateFilterApply = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handleDateFilterClear = () => {
    setDateFilter("");
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const columns = [
    {
      id: "ChangeDate",
      label: "Change Date",
      description: "Date and time of the change",
    },
    {
      id: "LastModifiedBy",
      label: "Modified By",
      description: "User who made the change",
    },
    {
      id: "ChangeType",
      label: "Change Type",
      description: "Type of change made",
    },
    {
      id: "OldValue",
      label: "Old Value",
      description: "Previous value before change",
    },
    {
      id: "NewValue",
      label: "New Value",
      description: "New value after change",
    },
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>Change History for Store {nutroStoreID}</DialogTitle>
      {/* <DialogContent dividers>
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
            <TextField
              label="Filter by Date (MMDDYYYY)"
              value={dateFilter}
              onChange={handleDateFilterChange}
              size="small"
              placeholder="e.g., 05272025"
            />
            <Button
              variant="contained"
              onClick={handleDateFilterApply}
              disabled={isChangeLogLoading}
            >
              Apply
            </Button>
            <Button
              variant="outlined"
              onClick={handleDateFilterClear}
              disabled={isChangeLogLoading}
            >
              Clear
            </Button>
          </Box>
        </Box>

        {isChangeLogLoading ? (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: 200,
            }}
          >
            <CircularProgress />
          </Box>
        ) : (
          <TableComponent
            columns={columns}
            rows={changeLog}
            rowsPerPage={pagination.rowsPerPage}
            page={pagination.page}
            totalCount={changeLogTotalCount}
            onPageChange={handlePageChange}
            showActions={false}
            isLoading={isChangeLogLoading}
          />
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary" disabled={isChangeLogLoading}>
          Close
        </Button>
      </DialogActions> */}
    </Dialog>
  );
};

export default ChangeLogModal;
