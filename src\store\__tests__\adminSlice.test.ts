import adminReducer, {
    setAttributeFilters,
    setUserFilters,
    resetAttributeFilters,
    resetUserFilters,
    setCurrentAttribute,
    setCurrentUser,
    clearCurrentUser,
  } from '../slice/adminSlice';
  import { FilterOption, AdminState, AttributeItem, User } from '../../types/admin.types';
import { getAttributeListItems } from '../../services/action/attribute.action';
import { getUsers, createUser, updateUser, deleteUser, getAllRoles, getAllManagers, assignUserRole } from '../../services/action/user.action';
  
  describe('adminSlice', () => {
    const initialState: AdminState = {
      filters: {
        attributeFilters: {
          attribute: null,
          storeId: "",
        },
        userFilters: {
          active: null,
        },
      },
      currentAttribute: null,
      currentUser: null,
      listNames: [],
      attributes: [],
      users: [],
      roles: [],
      managers: [],
      isLoading: false,
      error: null,
    };
  
    describe('setAttributeFilters', () => {
      it('should update attribute filters', () => {
        const newFilter: FilterOption = { label: 'Test Attribute', value: 'test' };
        const action = setAttributeFilters({ attribute: newFilter, storeId: 'store1' });
        const result = adminReducer(initialState, action);
  
        expect(result.filters.attributeFilters.attribute).toEqual(newFilter);
        expect(result.filters.attributeFilters.storeId).toBe('store1');
      });
    });
  
    describe('setUserFilters', () => {
      it('should update user filters', () => {
        const newFilter: FilterOption = { label: 'Active', value: 'true' };
        const action = setUserFilters({ active: newFilter });
        const result = adminReducer(initialState, action);
  
        expect(result.filters.userFilters.active).toEqual(newFilter);
      });
    });
  
    describe('resetAttributeFilters', () => {
      it('should reset attribute filters to initial state', () => {
        const modifiedState = {
          ...initialState,
          filters: {
            ...initialState.filters,
            attributeFilters: {
              attribute: { label: 'Test', value: 'test' },
              storeId: 'store1'
            }
          }
        };
        
        const result = adminReducer(modifiedState, resetAttributeFilters());
        
        expect(result.filters.attributeFilters).toEqual(initialState.filters.attributeFilters);
      });
    });
  
    describe('resetUserFilters', () => {
      it('should reset user filters to initial state', () => {
        const modifiedState = {
          ...initialState,
          filters: {
            ...initialState.filters,
            userFilters: {
              active: { label: 'Active', value: 'true' }
            }
          }
        };
        
        const result = adminReducer(modifiedState, resetUserFilters());
        
        expect(result.filters.userFilters).toEqual(initialState.filters.userFilters);
      });
    });
  
    describe('setCurrentAttribute', () => {
      it('should set the current attribute', () => {
        const attribute: AttributeItem = {
          RecID: 1,
          ListName: 'TestList',
          ItemName: 'TestItem',
          ItemValue: 'test',
          ItemDescription: 'Test Description'
        };
        
        const result = adminReducer(initialState, setCurrentAttribute(attribute));
        
        expect(result.currentAttribute).toEqual(attribute);
      });
    });
  
    describe('setCurrentUser', () => {
      it('should set the current user', () => {
        const user: User = {
          UserID: '1',
          UserLogin: 'testuser',
          UserFullName: 'Test User',
          UserEmail: '<EMAIL>',
          Active: true
        };
        
        const result = adminReducer(initialState, setCurrentUser(user));
        
        expect(result.currentUser).toEqual(user);
      });
    });
  
    describe('clearCurrentUser', () => {
      it('should clear the current user', () => {
        const stateWithUser = {
          ...initialState,
          currentUser: {
            UserID: '1',
            UserLogin: 'testuser',
            UserFullName: 'Test User'
          }
        };
        
        const result = adminReducer(stateWithUser, clearCurrentUser());
        
        expect(result.currentUser).toBeNull();
      });
    });
  
    // Testing extraReducers would require mocking the API calls
    describe('extraReducers', () => {
      describe('getAttributeListItems', () => {
        it('should handle pending state', () => {
          const result = adminReducer(initialState, getAttributeListItems.pending);
          expect(result.isLoading).toBe(true);
          expect(result.error).toBeNull();
        });
  
        it('should handle fulfilled state', () => {
          const mockAttributes: AttributeItem[] = [
            {
              RecID: 1,
              ListName: 'List1',
              ItemName: 'Item1',
              ItemValue: 'value1'
            }
          ];
          const result = adminReducer(
            initialState,
            getAttributeListItems.fulfilled(mockAttributes, 'requestId', { listName: 'List1' })
          );
          expect(result.isLoading).toBe(false);
          expect(result.attributes).toEqual(mockAttributes);
        });
  
        it('should handle rejected state', () => {
          const error = new Error('Failed to fetch');
          const result = adminReducer(
            initialState,
            getAttributeListItems.rejected(error, 'requestId', { listName: 'List1' })
          );
          expect(result.isLoading).toBe(false);
          expect(result.error).toBe('Failed to fetch');
        });
      });
  
      describe('getUsers', () => {
        it('should handle pending state', () => {
          const result = adminReducer(initialState, getUsers.pending);
          expect(result.isLoading).toBe(true);
          expect(result.error).toBeNull();
        });
  
        it('should handle fulfilled state', () => {
          const mockUsers: User[] = [
            {
              UserID: '1',
              UserLogin: 'user1',
              UserFullName: 'User One'
            }
          ];
          const result = adminReducer(
            initialState,
            getUsers.fulfilled(mockUsers, 'requestId')
          );
          expect(result.isLoading).toBe(false);
          expect(result.users).toEqual(mockUsers);
        });
  
        it('should handle rejected state', () => {
          const error = new Error('Failed to fetch users');
          const result = adminReducer(
            initialState,
            getUsers.rejected(error, 'requestId')
          );
          expect(result.isLoading).toBe(false);
          expect(result.error).toBe('Failed to fetch users');
        });
      });
  
      describe('createUser', () => {
        it('should handle pending state', () => {
          const result = adminReducer(initialState, createUser.pending);
          expect(result.isLoading).toBe(true);
          expect(result.error).toBeNull();
        });
  
        it('should handle fulfilled state by adding new user', () => {
          const newUser: User = {
            UserID: '2',
            UserLogin: 'newuser',
            UserFullName: 'New User'
          };
          const result = adminReducer(
            initialState,
            createUser.fulfilled(newUser, 'requestId', {
              UserLogin: 'newuser',
            })
          );
          expect(result.isLoading).toBe(false);
          expect(result.users).toContainEqual(newUser);
        });
  
        it('should handle rejected state', () => {
          const error = new Error('Failed to create user');
          const result = adminReducer(
            initialState,
            createUser.rejected(error, 'requestId', {
              UserLogin: 'newuser',
            })
          );
          expect(result.isLoading).toBe(false);
          expect(result.error).toBe('Failed to create user');
        });
      });
  
      describe('updateUser', () => {
        const existingUser: User = {
          UserID: '1',
          UserLogin: 'user1',
          UserFullName: 'User One'
        };
  
        it('should handle pending state', () => {
          const result = adminReducer(
            { ...initialState, users: [existingUser] },
            updateUser.pending
          );
          expect(result.isLoading).toBe(true);
          expect(result.error).toBeNull();
        });
  
        it('should handle fulfilled state by updating existing user', () => {
          const updatedUser = {
            ...existingUser,
            UserFullName: 'Updated User One'
          };
          const result = adminReducer(
            { ...initialState, users: [existingUser], currentUser: existingUser },
            updateUser.fulfilled(updatedUser, 'requestId', {
              UserID: '1',
              UserFullName: 'Updated User One'
            })
          );
          expect(result.isLoading).toBe(false);
          expect(result.users[0].UserFullName).toBe('Updated User One');
          expect(result.currentUser?.UserFullName).toBe('Updated User One');
        });
  
        it('should handle rejected state', () => {
          const error = new Error('Failed to update user');
          const result = adminReducer(
            { ...initialState, users: [existingUser] },
            updateUser.rejected(error, 'requestId', {
              UserID: '1',
              UserFullName: 'Updated User One'
            })
          );
          expect(result.isLoading).toBe(false);
          expect(result.error).toBe('Failed to update user');
        });
      });
  
      describe('deleteUser', () => {
        const existingUser: User = {
          UserID: '1',
          UserLogin: 'user1',
          UserFullName: 'User One'
        };
  
        it('should handle pending state', () => {
          const result = adminReducer(
            { ...initialState, users: [existingUser] },
            deleteUser.pending
          );
          expect(result.isLoading).toBe(true);
          expect(result.error).toBeNull();
        });
  
        it('should handle fulfilled state by removing user', () => {
          const result = adminReducer(
            { ...initialState, users: [existingUser], currentUser: existingUser },
            deleteUser.fulfilled('1', 'requestId', '1')
          );
          expect(result.isLoading).toBe(false);
          expect(result.users).toHaveLength(0);
          expect(result.currentUser).toBeNull();
        });
  
        it('should handle rejected state', () => {
          const error = new Error('Failed to delete user');
          const result = adminReducer(
            { ...initialState, users: [existingUser] },
            deleteUser.rejected(error, 'requestId', '1')
          );
          expect(result.isLoading).toBe(false);
          expect(result.error).toBe('Failed to delete user');
        });
      });
  
      describe('getAllRoles', () => {
        it('should handle fulfilled state', () => {
          const mockRoles = [
            { RoleID: '1', RoleName: 'Admin' },
            { RoleID: '2', RoleName: 'User' }
          ];
          const result = adminReducer(
            initialState,
            getAllRoles.fulfilled(mockRoles, 'requestId')
          );
          expect(result.roles).toEqual(mockRoles);
        });
      });
  
      describe('getAllManagers', () => {
        it('should handle fulfilled state', () => {
          const mockManagers = [
            { UserID: '1', UserLogin: 'manager1' },
            { UserID: '2', UserLogin: 'manager2' }
          ];
          const result = adminReducer(
            initialState,
            getAllManagers.fulfilled(mockManagers, 'requestId')
          );
          expect(result.managers).toEqual(mockManagers);
        });
      });
  
      describe('assignUserRole', () => {
        const existingUser: User = {
          UserID: '1',
          UserLogin: 'user1',
          RoleID: '2',
          RoleName: 'User'
        };
  
        const existingRole = {
          RoleID: '1',
          RoleName: 'Admin'
        };
  
        it('should handle fulfilled state by updating user role', () => {
          const state = {
            ...initialState,
            users: [existingUser],
            roles: [existingRole],
            currentUser: existingUser
          };
  
          const result = adminReducer(
            state,
            assignUserRole.fulfilled({ userId: '1', roleId: '1' }, 'requestId', {
              userId: '1',
              roleId: '1'
            })
          );
  
          expect(result.users[0].RoleID).toBe('1');
          expect(result.users[0].RoleName).toBe('Admin');
          expect(result.currentUser?.RoleID).toBe('1');
          expect(result.currentUser?.RoleName).toBe('Admin');
        });
      });
    });
  });