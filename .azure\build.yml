name: Build

trigger:
  branches:
    include:
      - main

variables:
  vmImageName: "ubuntu-22.04"

stages:
  - stage: Build
    displayName: Build stage
    jobs:
      - job: Build
        displayName: "Build"
        pool:
          vmImage: $(vmImageName)
        steps:
          - task: npmAuthenticate@0
            inputs:
              workingFile: .npmrc
              customEndpoint: mars-react-components-npm-registry

          - task: NodeTool@0
            inputs:
              versionSpec: "18.x"
            displayName: "Install Node.js"

          - script: |
              npm ci
            displayName: "install dependencies"

          - script: |
              npm run linter:check
            displayName: "linter check"

          - script: |
              npm run prettier:check
            displayName: "prettier check"

          - script: |
              npm run test:ci
            displayName: "unit tests"

          - script: |
              npm run build:prod
            displayName: "build application"
