import React, { useState, useEffect } from "react";
import {
  Box,
  Chip,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  SelectChangeEvent,
  TextField,
  Checkbox,
  ListItemText,
} from "@mui/material";
import { Cancel } from "@mui/icons-material";

interface MultiSelectFilterProps {
  name: string;
  label: string;
  value: string[];
  options: { value: string; label: string }[];
  onChange: (value: string[]) => void;
  searchable?: boolean;
}

const MultiSelectFilter: React.FC<MultiSelectFilterProps> = ({
  name,
  label,
  value = [],
  options,
  onChange,
  searchable = true,
}) => {
  const [searchText, setSearchText] = useState("");
  const [filteredOptions, setFilteredOptions] = useState(options);

  useEffect(() => {
    if (searchText) {
      setFilteredOptions(
        options.filter((option) => option.label.toLowerCase().includes(searchText.toLowerCase())),
      );
    } else {
      setFilteredOptions(options);
    }
  }, [searchText, options]);

  const handleChange = (event: SelectChangeEvent<typeof value>) => {
    const {
      target: { value: selectedValues },
    } = event;
    onChange(typeof selectedValues === "string" ? selectedValues.split(",") : selectedValues);
  };

  const handleDelete = (valueToDelete: string) => {
    onChange(value.filter((item) => item !== valueToDelete));
  };

  return (
    <FormControl sx={{ minWidth: 200 }} size="small">
      <InputLabel id={`${name}-label`}>{label}</InputLabel>
      <Select
        labelId={`${name}-label`}
        id={name}
        multiple
        value={value}
        onChange={handleChange}
        input={<OutlinedInput label={label} />}
        renderValue={(selected) => (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
            {selected.map((selectedValue) => {
              const option = options.find((opt) => opt.value === selectedValue);
              return (
                <Chip
                  key={selectedValue}
                  label={option?.label || selectedValue}
                  onDelete={() => handleDelete(selectedValue)}
                  deleteIcon={<Cancel onMouseDown={(event) => event.stopPropagation()} />}
                />
              );
            })}
          </Box>
        )}
        MenuProps={{
          PaperProps: {
            style: {
              maxHeight: 300,
            },
          },
        }}
      >
        {searchable && (
          <Box sx={{ p: 1 }}>
            <TextField
              size="small"
              fullWidth
              placeholder="Search..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onClick={(e) => e.stopPropagation()}
            />
          </Box>
        )}
        {filteredOptions.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            <Checkbox checked={value.includes(option.value)} />
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default MultiSelectFilter;
