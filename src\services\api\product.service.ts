import axiosInstance from '../../axiosConfig';

// Product interfaces
export interface Product {
  PRDNO: string;
  DESCP: string;
  ItemID: string;
  SkuName: string;
  AssociatedItem: string;
  REUPC?: string;
  Brand?: string;
  SalesDesc?: string;
  // Additional fields that might be returned by the API
  id?: string | number;
  createdAt?: string;
  updatedAt?: string;
  status?: string;
  category?: string;
  price?: number;
  currency?: string;
  availability?: boolean;
  [key: string]: any; // Allow for additional dynamic fields
}

export interface ProductSearchParams {
  page?: number;
  limit?: number;
  PRDNO?: string;
  DESCP?: string;
  REUPC?: string;
  Brand?: string;
  SalesDesc?: string;
  AssocItem?: string;
  search?: string; // Generic search parameter
}

export interface ProductSearchResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ListItem {
  value: string;
  label: string;
}

export interface ListItemsResponse {
  brands?: ListItem[];
  [key: string]: ListItem[] | undefined;
}

// Product API service class
class ProductService {
  private baseUrl = '/products';

  /**
   * Get list items for dropdowns (brands, etc.)
   */
  async getListItems(): Promise<ListItemsResponse> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/list-items`);
      return response.data;
    } catch (error) {
      console.error('Error fetching list items:', error);
      throw new Error('Failed to fetch list items');
    }
  }

  /**
   * Search products with filters and pagination
   */
  async searchProducts(params: ProductSearchParams): Promise<ProductSearchResponse> {
    try {
      // Clean up empty parameters
      const cleanParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      const response = await axiosInstance.get(`${this.baseUrl}/search`, {
        params: cleanParams,
      });
      
      return response.data;
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string): Promise<Product> {
    try {
      const response = await axiosInstance.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }
}

// Export singleton instance
export const productService = new ProductService();
export default productService;
