import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import MenuItemWithDropdown from '../MenuItemWithDropdown';
import { useNavigate } from 'react-router-dom';
import { Menu, MenuItem } from '@mars/vizx-react';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('@mars/vizx-react', () => ({
  Button: vi.fn(({ children, onClick }) => (
    <button onClick={onClick} data-testid="dropdown-button">
      {children}
    </button>
  )),
  Menu: vi.fn(({ children, open }) => (
    open ? <div data-testid="dropdown-menu">{children}</div> : null
  )),
  MenuItem: vi.fn(({ children, onClick }) => (
    <div onClick={onClick} data-testid="menu-item">
      {children}
    </div>
  )),
}));

describe('MenuItemWithDropdown Component', () => {
  const mockNavigate = vi.fn();
  const mockMenuItems = [
    { id: '/item1', title: 'Item 1' },
    { id: '/item2', title: 'Item 2' },
  ];

  beforeEach(() => {
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
  });

  it('renders dropdown button with label', () => {
    render(
      <MenuItemWithDropdown 
        label="Test" 
        menuItems={mockMenuItems} 
        selectedMenuItem="/" 
      />
    );
    expect(screen.getByTestId('dropdown-button')).toHaveTextContent('Test ▾');
  });

  it('opens menu when clicked', () => {
    render(
      <MenuItemWithDropdown 
        label="Test" 
        menuItems={mockMenuItems} 
        selectedMenuItem="/" 
      />
    );
    fireEvent.click(screen.getByTestId('dropdown-button'));
    expect(screen.getByTestId('dropdown-menu')).toBeInTheDocument();
  });

  it('navigates when menu item is clicked', () => {
    render(
      <MenuItemWithDropdown 
        label="Test" 
        menuItems={mockMenuItems} 
        selectedMenuItem="/" 
      />
    );
    fireEvent.click(screen.getByTestId('dropdown-button'));
    fireEvent.click(screen.getAllByTestId('menu-item')[0]);
    expect(mockNavigate).toHaveBeenCalledWith('/item1');
  });

  // it('applies active style when child item is selected', () => {
  //   const { rerender } = render(
  //     <MenuItemWithDropdown 
  //       label="Test" 
  //       menuItems={mockMenuItems} 
  //       selectedMenuItem="/item1" 
  //     />
  //   );
  //   expect(screen.getByTestId('dropdown-button')).toHaveStyle('border-bottom: 2px solid white');
    
  //   rerender(
  //     <MenuItemWithDropdown 
  //       label="Test" 
  //       menuItems={mockMenuItems} 
  //       selectedMenuItem="/other" 
  //     />
  //   );
  //   expect(screen.getByTestId('dropdown-button')).toHaveStyle('border-bottom: none');
  // });
});