import { RouterProvider } from "react-router-dom";
import AppLayout from "../pages/AppLayout";
import ErrorPage from "../pages/ErrorPage";
import Home from "../pages/Home";
import PageNotFound from "../pages/PageNotFound";
import { createRouter } from "./createRouter";

// Newly added pages
import AttributeList from "../pages/admin/AttributeList";
import UserList from "../pages/admin/UsersList";
import StoreSearch from "../pages/store/StoreSearch";
import NewUser from "../pages/admin/NewUser";
import StoreDetailsPage from "../components/store/StoreDetails";
import MappedStoreSearch from "../pages/store/MappedSearch";
import UnmappedStoreSearch from "../pages/store/UnMappedSearch";
import StoreDescDetailsPage from "../components/store/StoreDescDetails";
import StoreAttributionPage from "../pages/store/StoreAttribution";

// Product pages
import ProductSearch from "../pages/product/ProductSearch";
import ProductAttribution from "../pages/product/ProductAttribution";
import UnmappedProductSearch from "../pages/product/UnmappedProductSearch";

const routes = [
  {
    path: "",
    errorElement: <ErrorPage />,
    children: [
      {
        path: "/",
        element: <AppLayout />,
        children: [
          {
            index: true,
            element: <Home />,
          },
          // Admin Menu Pages
          {
            path: "attribute-list",
            element: <AttributeList />,
          },
          {
            path: "user-list",
            element: <UserList />,
          },
          {
            path: "new-user",
            element: <NewUser />,
          },
          // Store Menu Pages
          {
            path: "store-search",
            element: <StoreSearch />,
          },
          {
            path: "unmapped-store-search",
            element: <UnmappedStoreSearch />,
          },
          {
            path: "mapped-store-search",
            element: <MappedStoreSearch />,
          },
          {
            path: "/store-search/:nutroStoreID",
            element: <StoreDetailsPage />,
          },
          {
            path: "/store-desc/:storeId",
            element: <StoreDescDetailsPage />,
          },
          {
            path: "/store-attribute",
            element: <StoreAttributionPage />,
          },
          // Product Menu Pages
          {
            path: "product-search",
            element: <ProductSearch />,
          },
          {
            path: "unmapped-product-search",
            element: <UnmappedProductSearch />,
          },
          {
            path: "product-attribute",
            element: <ProductAttribution />,
          },
        ],
      },
      {
        path: "*",
        element: <PageNotFound />,
      },
    ],
  },
];

export const Routes = () => {
  return <RouterProvider router={createRouter(routes)} />;
};
