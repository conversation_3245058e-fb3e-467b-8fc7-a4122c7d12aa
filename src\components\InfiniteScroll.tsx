import React, { useState, useEffect, useRef, useCallback } from "react";
import { Autocomplete, TextField, CircularProgress, Box, Chip } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../store";
import { AppDispatch } from "../store";
import { getStoreFilterValues } from "../services/action/store.action";

interface InfiniteScrollDropdownProps {
  field: string;
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
}

const InfiniteScrollDropdown: React.FC<InfiniteScrollDropdownProps> = ({
  field,
  label,
  value,
  onChange,
  disabled = false,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [dropdownLoading, setDropdownLoading] = useState(false); // Local loading state
  const [hasMore, setHasMore] = useState(true);
  const listRef = useRef<HTMLUListElement>(null);
  const loadingRef = useRef(false);
  const debounceTimer = useRef<NodeJS.Timeout>();

  const { storeFilterValues } = useSelector((state: RootState) => state.store);
  const currentFieldData = storeFilterValues[field] || { values: [], total: 0 };
  const options = Array.isArray(currentFieldData.values) ? currentFieldData.values : [];
  const loadedCount = options.length;

  const loadMore = useCallback(
    async (search = "", offset = 0) => {
      if (loadingRef.current) return;

      try {
        loadingRef.current = true;
        setDropdownLoading(true); // Only set local loading state

        await dispatch(
          getStoreFilterValues({
            field,
            search,
            limit: 100,
            offset,
          }),
        ).unwrap();

        setHasMore(offset + 100 < currentFieldData.total);
      } finally {
        loadingRef.current = false;
        setDropdownLoading(false);
      }
    },
    [dispatch, field, currentFieldData.total],
  );

  // Load initial data when dropdown opens
  const handleOpen = useCallback(() => {
    setOpen(true);
    if (options.length === 0) {
      loadMore(inputValue, 0);
    }
  }, [inputValue, loadMore, options.length]);

  // Handle search input changes with debounce
  useEffect(() => {
    if (!open) return;

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      loadMore(inputValue, 0);
    }, 500);

    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, [inputValue, open, loadMore]);

  const handleScroll = useCallback(() => {
    if (!listRef.current || dropdownLoading || !hasMore) return;

    const list = listRef.current;
    const scrollBottom = list.scrollTop + list.clientHeight;
    const threshold = list.scrollHeight - 50;

    if (scrollBottom >= threshold) {
      loadMore(inputValue, loadedCount);
    }
  }, [dropdownLoading, hasMore, loadMore, inputValue, loadedCount]);

  return (
    <Autocomplete
      multiple
      disabled={disabled}
      open={open}
      sx={{ minWidth: 180 }}
      onOpen={handleOpen}
      onClose={() => setOpen(false)}
      options={options}
      value={value}
      onChange={(_, newValue) => onChange(newValue as string[])}
      inputValue={inputValue}
      onInputChange={(_, newInputValue) => setInputValue(newInputValue)}
      loading={dropdownLoading} // Use local loading state
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          variant="outlined"
          fullWidth
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {dropdownLoading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        />
      )}
      renderTags={(value, getTagProps) =>
        value.map((option, index) => (
          <Chip key={option} label={option} {...getTagProps({ index })} size="small" />
        ))
      }
      ListboxProps={{
        onScroll: handleScroll,
        ref: listRef,
        style: { maxHeight: 200, overflow: "auto" },
      }}
      getOptionLabel={(option) => option.toString()}
      noOptionsText={inputValue ? "No matches found" : "Type to search"}
    />
  );
};

export default InfiniteScrollDropdown;
