import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Product, ListItemsResponse, ProductSearchResponse } from '../../services/api/product.service';
import { fetchListItems, searchProducts, fetchProductById } from '../../services/action/product.action';

interface ProductState {
  // List items for dropdowns
  listItems: ListItemsResponse;
  listItemsLoading: boolean;
  listItemsError: string | null;

  // Product search
  products: Product[];
  searchLoading: boolean;
  searchError: string | null;
  totalCount: number;
  currentPage: number;
  totalPages: number;

  // Single product
  selectedProduct: Product | null;
  productLoading: boolean;
  productError: string | null;
}

const initialState: ProductState = {
  listItems: {},
  listItemsLoading: false,
  listItemsError: null,

  products: [],
  searchLoading: false,
  searchError: null,
  totalCount: 0,
  currentPage: 1,
  totalPages: 0,

  selectedProduct: null,
  productLoading: false,
  productError: null,
};

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    clearSearchResults: (state) => {
      state.products = [];
      state.totalCount = 0;
      state.currentPage = 1;
      state.totalPages = 0;
      state.searchError = null;
    },
    clearSelectedProduct: (state) => {
      state.selectedProduct = null;
      state.productError = null;
    },
    clearErrors: (state) => {
      state.listItemsError = null;
      state.searchError = null;
      state.productError = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch list items
    builder
      .addCase(fetchListItems.pending, (state) => {
        state.listItemsLoading = true;
        state.listItemsError = null;
      })
      .addCase(fetchListItems.fulfilled, (state, action: PayloadAction<ListItemsResponse>) => {
        state.listItemsLoading = false;
        state.listItems = action.payload;
      })
      .addCase(fetchListItems.rejected, (state, action) => {
        state.listItemsLoading = false;
        state.listItemsError = action.payload || 'Failed to fetch list items';
      });

    // Search products
    builder
      .addCase(searchProducts.pending, (state) => {
        state.searchLoading = true;
        state.searchError = null;
      })
      .addCase(searchProducts.fulfilled, (state, action: PayloadAction<ProductSearchResponse>) => {
        state.searchLoading = false;
        state.products = action.payload.data;
        state.totalCount = action.payload.total;
        state.currentPage = action.payload.page;
        state.totalPages = action.payload.totalPages;
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.searchLoading = false;
        state.searchError = action.payload || 'Failed to search products';
      });

    // Fetch product by ID
    builder
      .addCase(fetchProductById.pending, (state) => {
        state.productLoading = true;
        state.productError = null;
      })
      .addCase(fetchProductById.fulfilled, (state, action: PayloadAction<Product>) => {
        state.productLoading = false;
        state.selectedProduct = action.payload;
      })
      .addCase(fetchProductById.rejected, (state, action) => {
        state.productLoading = false;
        state.productError = action.payload || 'Failed to fetch product';
      });
  },
});

export const { clearSearchResults, clearSelectedProduct, clearErrors } = productSlice.actions;
export default productSlice.reducer;
