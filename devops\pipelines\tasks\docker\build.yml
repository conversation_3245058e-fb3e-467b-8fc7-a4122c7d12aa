parameters:
- name: repository
  type: string
- name: dockerfilePath
  type: string
- name: tags
  type: string
- name: arguments
  type: string
  default: ''
- name: registryServiceConnection
  type: string

steps:
- task: Docker@2
  displayName: 'Build'
  inputs:
    repository: ${{parameters.repository}}
    containerRegistry: ${{parameters.registryServiceConnection}}
    command: 'Build'
    dockerfilePath: ${{parameters.dockerfilePath}}
    tags: ${{parameters.tags}}
    arguments: ${{parameters.arguments}}