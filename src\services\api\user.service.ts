import client from "../../axiosConfig";

interface UserParams {
  limit?: number;
  page?: number;
  active?: string;
  search_query?: string;
  user_type?: string;
}

interface User {
  UserID?: string;
  UserLogin: string;
  UserFirstName?: string;
  UserLastName?: string;
  UserFullName?: string;
  UserEmail?: string;
  MarsUsername?: string;
  UserType?: string;
  ManagerID?: string;
  IsLockedOut?: boolean;
  Active?: boolean;
  RoleID?: string;
}

interface Role {
  RoleID: string;
  RoleName: string;
  RoleDescription?: string;
}

const UserService = {
  async getUsers(params: UserParams) {
    // return client.get("/users/", { params });
    const response = await client.get("/users/", { params });
    return {
      users: response.data.userList,
      totalCount: response.data.totalCount,
    };
  },

  getAllRoles() {
    return client.get("/users/roles/");
  },

  getAllManagers() {
    return client.get("/users/managers/");
  },

  createUser(data: User) {
    return client.post("/users/", data);
  },

  getUser(id: string) {
    return client.get(`/users/${id}`);
  },

  updateUser(id: string, data: User) {
    return client.put(`/users/${id}`, data);
  },

  patchUser(id: string, data: Partial<User>) {
    return client.patch(`/users/${id}`, data);
  },

  deleteUser(id: string) {
    return client.delete(`/users/${id}`);
  },

  assignUserRole(userId: string, roleId: string) {
    return client.post(`/users/${userId}/roles/`, { RoleID: roleId, UserID: userId });
  },
};

export default UserService;
