import {
  AnyAction,
  combineReducers,
  configureStore,
  Middleware,
  ThunkDispatch,
} from "@reduxjs/toolkit";
import adminReducer from "./slice/adminSlice";
import storeReducer from "./slice/storeSlice";
import productReducer from "./slice/productSlice";

const development: boolean = process.env.NODE_ENV === "development";
const middleware: Middleware[] = [];

// if (development) {
//   const logger = createLogger({
//     collapsed: (getState, action, logEntry) => !logEntry?.error,
//   })
//   middleware.push(logger)
// }

const reducer = combineReducers({
  admin: adminReducer,
  store: storeReducer,
  product: productReducer,
});

const store = configureStore({
  reducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(middleware),
  devTools: development,
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
export type TypedDispatch<T> = ThunkDispatch<T, any, AnyAction>;

export default store;
