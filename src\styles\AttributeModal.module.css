.modalContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 800px;
  max-width: 95%;
  max-height: 90vh;
  overflow-y: auto;
  padding: 24px 16px; /* Reduced left-right padding */
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.modalTitle {
  font-family: "Mars Centra", sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0%;
  text-transform: uppercase;
  margin: 0 0 24px 0;
  padding: 0 8px;
}

.labelText {
  font-family: "Mars Centra", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0%;
  background: rgba(60, 60, 112, 1);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
}

.formContainer {
  padding: 0 8px; /* Added padding to match reduced container padding */
  margin-bottom: 24px;
}

.formField {
  margin-bottom: 16px;
}

.formDivider {
  margin: 24px 0;
  border: none;
  border-top: 1px solid #ddd;
}

.actionButtons {
  padding: 0 8px;
  display: flex;
  gap: 12px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .modalContainer {
    width: 90%;
    padding: 16px 12px;
  }
  
  .modalTitle {
    padding: 0 4px;
  }
  
  .formContainer {
    padding: 0 4px;
  }
  
  .actionButtons {
    padding: 0 4px;
  }
}