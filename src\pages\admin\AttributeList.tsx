import React, { useState, useEffect, useCallback } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import AttributeModal from "../../components/AttributeModal";
import ConfirmationDialog from "../../components/ConfirmationDialog";
import { useDispatch, useSelector } from "react-redux";
import AddIcon from "@mui/icons-material/Add";
import { RootState } from "../../store";
import {
  setCurrentAttribute,
  setAttributeFilters,
  resetAttributeFilters,
} from "../../store/slice/adminSlice";
import {
  getAttributeListItems,
  createAttributeListItem,
  updateAttributeListItem,
  deleteAttributeListItem,
  getAllListNames,
} from "../../services/action/attribute.action";
import { Box, Button, debounce } from "@mui/material";
import FilterRow from "../../components/FilterRow";
import { toast } from "react-toastify";
import { UnknownAction } from "@reduxjs/toolkit";
import { ThunkDispatch } from "redux-thunk";
import { AttributeItem } from "../../types/admin.types";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

const AttributeList = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [attributeToDelete, setAttributeToDelete] = useState<AttributeItem | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 20,
  });

  const dispatch: AppDispatch = useDispatch();
  const { attributes, isLoading, currentAttribute, listNames, totalCountAttributes } = useSelector(
    (state: RootState) => state.admin,
  );

  const { attribute } = useSelector((state: RootState) => state.admin.filters.attributeFilters);

  // Fetch list names on component mount
  useEffect(() => {
    dispatch(getAllListNames());
  }, [dispatch]);

  // Fetch attributes with current filters and search query
  const fetchAttributes = useCallback(() => {
    dispatch(
      getAttributeListItems({
        limit: pagination.rowsPerPage,
        page: pagination.page,
        list_name: attribute?.value || "",
        search_query: searchQuery,
      }),
    );
  }, [dispatch, attribute, searchQuery, pagination.page, pagination.rowsPerPage]);

  // Initial fetch and when filters/search change
  useEffect(() => {
    fetchAttributes();
  }, [fetchAttributes]);

  // Debounced search handler
  const handleSearch = useCallback(
    debounce((query: string) => {
      setSearchQuery(query);
      setPagination((prev) => ({ ...prev, page: 1 }));
    }, 500),
    [],
  );

  const attributeOptions = [
    ...listNames.map((name) => ({
      value: name,
      label: name,
    })),
  ];

  const handleEdit = (row: AttributeItem) => {
    setModalMode("edit");
    dispatch(setCurrentAttribute(row));
    setModalOpen(true);
  };

  const handleDeleteClick = (row: AttributeItem) => {
    setAttributeToDelete(row);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!attributeToDelete) return;

    try {
      await dispatch(deleteAttributeListItem(attributeToDelete.RecID)).unwrap();
      toast.success("Attribute deleted successfully");
      dispatch(getAllListNames());
      fetchAttributes();
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message || "Failed to delete attribute");
      } else {
        toast.error("Failed to delete attribute");
      }
    } finally {
      setDeleteDialogOpen(false);
      setAttributeToDelete(null);
    }
  };

  const handleAddNew = () => {
    setModalMode("add");
    dispatch(setCurrentAttribute(null));
    setModalOpen(true);
  };

  const handleSaveAttribute = async (attributeData: AttributeItem) => {
    try {
      if (!attributeData.ListName || !attributeData.ItemValue) {
        toast.error("ListName and ItemValue are required fields");
        return;
      }

      if (modalMode === "add") {
        await dispatch(createAttributeListItem(attributeData)).unwrap();
        toast.success("Attribute created successfully");
        dispatch(getAllListNames());
        fetchAttributes();
      } else if (currentAttribute) {
        await dispatch(
          updateAttributeListItem({
            id: currentAttribute.RecID,
            data: attributeData,
          }),
        ).unwrap();
        toast.success("Attribute updated successfully");
        dispatch(getAllListNames());
        fetchAttributes();
      }
      setModalOpen(false);
    } catch (error) {
      const errorMessage = (() => {
        if (typeof error === "string") return error;
        if (error && typeof error === "object") {
          if ("response" in error && error.response?.data?.message) {
            return error.response.data.message;
          }
          if ("message" in error) return error.message;
          return JSON.stringify(error);
        }
        return "Unknown error";
      })();

      if (errorMessage.includes("409")) {
        toast.error("Item with this name already exists");
      } else {
        toast.error(`Failed to ${modalMode} attribute`);
      }
    }
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const columns = [
    {
      id: "ListName",
      label: "List Name",
      description: "The category or group this attribute belongs to",
    },
    {
      id: "ItemName",
      label: "Item Name",
      description: "The name of the attribute item",
    },
    {
      id: "ItemValue",
      label: "Item Value",
      description: "The value associated with this attribute",
    },
    {
      id: "ItemOrder",
      label: "Item Order",
      description: "The display order of this item in the list",
    },
    {
      id: "ParentListName",
      label: "Parent List",
      description: "Optional parent list this item belongs to",
    },
  ];

  const handleResetFilters = () => {
    setSearchQuery("");
    setPagination((prev) => ({ ...prev, page: 1 }));
    dispatch(resetAttributeFilters());
  };

  return (
    <div>
      <TopBarLayout
        breadcrumbItems={["Admin", "Attributes"]}
        onSearchChange={(value) => {
          setSearchQuery(value);
          handleSearch(value);
        }}
      />

      <FilterRow
        filters={[
          {
            name: "attribute",
            label: "Attribute",
            type: "select",
            options: attributeOptions,
            value: attribute?.value || "",
            onChange: (value) => {
              const selectedOption = attributeOptions.find((opt) => opt.value === value);
              dispatch(
                setAttributeFilters({
                  attribute: selectedOption || null,
                }),
              );
              setPagination((prev) => ({ ...prev, page: 1 }));
            },
          },
        ]}
        onReset={handleResetFilters}
        showActionButtons={false}
      />

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          p: 1,
        }}
      >
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddNew}
          disabled={isLoading}
        >
          Add New
        </Button>
      </Box>

      <TableComponent
        columns={columns}
        rows={attributes}
        onEdit={handleEdit}
        onDelete={handleDeleteClick}
        rowsPerPage={pagination.rowsPerPage}
        page={pagination.page}
        totalCount={totalCountAttributes || 0}
        onPageChange={handlePageChange}
        isLoading={isLoading}
      />

      <AttributeModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSave={handleSaveAttribute}
        mode={modalMode}
        initialData={currentAttribute}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        title="Confirm Deletion"
        message={`Are you sure you want to delete attribute "${attributeToDelete?.ItemName}"? This action cannot be undone.`}
        onCancel={() => setDeleteDialogOpen(false)}
        onConfirm={handleDeleteConfirm}
        confirmText="Delete"
        cancelText="Cancel"
      />
    </div>
  );
};

export default AttributeList;
