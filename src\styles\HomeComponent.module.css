/* Home.css */

.home-container {
    display: flex;
    justify-content: center;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    background: linear-gradient(90deg, rgba(185, 185, 229, 0.25) 15.59%, rgba(185, 185, 229, 0) 100.85%);
    border-radius: 12px;
    height: 295px;
  }
  
  .left-section {
    max-width: 60%;
  }
  
  .right-section {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .app-capabilities {
    font-family: 'Mars Centra', sans-serif;
    font-weight: 700;
    font-size: 14px;
    margin-left: 2rem;
    margin-top: 2rem;
  }
  
  .capabilities-container {
    display: flex;
    justify-content: space-around;
    gap: 2rem;
    margin-top: 1.5rem;
    padding: 1.5rem;
    flex-wrap: wrap;
  }
  
  /* Make it responsive */
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      height: auto;
      align-items: center;
    }
  
    .left-section {
      max-width: 100%;
    }
  
    .capabilities-container {
      flex-direction: column;
      gap: 1rem;
    }
  }
  