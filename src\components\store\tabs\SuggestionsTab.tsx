import React from "react";
import { <PERSON>, Button } from "@mui/material";
import TableComponent from "../../TableComponent";
import styles from "../../../styles/Tabs.module.css";

interface SuggestionsTabProps {
  storeId: string;
  onMappedToClick: (nutroId: string) => void;
  onStoreSelect: (store: { VCID: number; NutrostoreID: number }) => void;
  selectedStore: { VCID: number; NutrostoreID: number } | null;
  suggestions: any[];
  isLoading: boolean;
  pagination: {
    page: number;
    rowsPerPage: number;
  };
  onPageChange: (newPage: number, newRowsPerPage: number) => void;
  totalCount?: number;
}

const SuggestionsTab: React.FC<SuggestionsTabProps> = ({
  storeId,
  onMappedToClick,
  onStoreSelect,
  selectedStore,
  suggestions,
  isLoading,
  pagination,
  onPageChange,
  totalCount = 0,
}) => {
  // Transform the API response to match our table structure
  const transformedSuggestions = suggestions.map((suggestion) => ({
    id: suggestion["NUTRO ID"],
    score: suggestion.SCORE,
    nutroId: suggestion["NUTRO ID"],
    storeName: suggestion.STORE,
    address: suggestion.ADDRESS,
    cityStateZip: `${suggestion.CITY}, ${suggestion.STATE} ${suggestion.ZIP}`,
    phone: suggestion.PHONE,
    status: suggestion.STATUS === "A" ? "Active" : "Inactive",
    territoryManager: suggestion["TERRITORY MANAGER"],
  }));

  const columns = [
    {
      id: "action",
      label: "Action",
      description: "Mapping actions",
      format: (value: string, row: any) => (
        <Button
          variant={selectedStore?.NutrostoreID === row.nutroId ? "contained" : "outlined"}
          size="small"
          onClick={() =>
            onStoreSelect({
              VCID: parseInt(storeId),
              NutrostoreID: parseInt(row.nutroId),
            })
          }
          sx={{
            textTransform: "none",
            minWidth: "80px",
          }}
        >
          {selectedStore?.NutrostoreID === row.nutroId ? "Selected" : "Select"}
        </Button>
      ),
    },
    {
      id: "score",
      label: "Match Score",
      description: "Percentage match confidence",
      format: (value: number) => `${value.toFixed(2)}%`,
      align: "right" as const,
    },
    {
      id: "nutroId",
      label: "Nutro ID",
      description: "Unique store identifier in Nutro system",
    },
    {
      id: "storeName",
      label: "Store Name",
      description: "Name of the suggested store",
    },
    {
      id: "address",
      label: "Address",
      description: "Physical address of the store",
    },
    {
      id: "cityStateZip",
      label: "City/State/ZIP",
      description: "Location information",
    },
    {
      id: "phone",
      label: "Phone",
      description: "Store contact number",
    },
    {
      id: "status",
      label: "Status",
      description: "Active/Inactive status",
      format: (value: string) => (
        <Box
          sx={{
            color: value ? "success.main" : "error.main",
            fontWeight: "bold",
          }}
        >
          {value}
        </Box>
      ),
    },
  ];

  return (
    <Box className={styles.tabContent}>
      <TableComponent
        columns={columns}
        rows={transformedSuggestions}
        rowsPerPage={pagination.rowsPerPage}
        page={pagination.page}
        onPageChange={onPageChange}
        showActions={false}
        isLoading={isLoading}
        totalCount={totalCount}
      />
    </Box>
  );
};

export default SuggestionsTab;
