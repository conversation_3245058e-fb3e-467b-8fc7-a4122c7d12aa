import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import Home from '../Home';
import type { CapabilityCardProps } from '../../components/Card';

// Mock the images
vi.mock('../../assets/mars-petcare.png', () => ({ default: 'mars-logo-mock' }));
vi.mock('../../assets/store.png', () => ({ default: 'store-logo-mock' }));
vi.mock('../../assets/product.png', () => ({ default: 'product-logo-mock' }));
vi.mock('../../assets/admin.png', () => ({ default: 'admin-logo-mock' }));

// Mock the CapabilityCard component with type safety
vi.mock('../../components/Card', () => ({
  default: vi.fn(({ title, route }: CapabilityCardProps) => (
    <div data-testid="capability-card" data-route={route}>
      {title}-card
    </div>
  )),
}));

describe('Home Component', () => {
  const renderHome = () =>
    render(
      <MemoryRouter>
        <Home />
      </MemoryRouter>
    );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Header Section', () => {
    it('renders the MPDDM title', () => {
      renderHome();
      expect(screen.getByRole('heading', { name: 'MPDDM' })).toBeInTheDocument();
    });

    it('renders the description text', () => {
      renderHome();
      expect(
        screen.getByText(/Nutro Product Distributor Data Center/i)
      ).toBeInTheDocument();
    });

    it('renders the Mars logo with correct attributes', () => {
      renderHome();
      const logo = screen.getByAltText('Mars Logo');
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute('src', 'mars-logo-mock');
    });
  });

  describe('App Capabilities Section', () => {
    it('renders the section heading', () => {
      renderHome();
      expect(
        screen.getByRole('heading', { name: 'App Capabilities' })
      ).toBeInTheDocument();
    });

    it('renders all capability cards with correct props', () => {
      renderHome();
      const cards = screen.getAllByTestId('capability-card');
      
      expect(cards).toHaveLength(3);
      expect(cards[0]).toHaveTextContent('Store-card');
      expect(cards[0]).toHaveAttribute('data-route', '/store-search');
      
      expect(cards[1]).toHaveTextContent('Product-card');
      expect(cards[1]).toHaveAttribute('data-route', '/product');
      
      expect(cards[2]).toHaveTextContent('Admin-card');
      expect(cards[2]).toHaveAttribute('data-route', '/attribute-list');
    });
  });

  // describe('Layout and Styling', () => {
  //   it('has the correct container structure', () => {
  //     const { container } = renderHome();
      
  //     // Verify main containers
  //     expect(container.querySelector('.home-container')).toBeInTheDocument();
  //     expect(container.querySelector('.header')).toBeInTheDocument();
      
  //     // Verify header sub-sections
  //     expect(container.querySelector('.left-section')).toBeInTheDocument();
  //     expect(container.querySelector('.right-section')).toBeInTheDocument();
      
  //     // Verify capabilities section
  //     expect(container.querySelector('.app-capabilities')).toBeInTheDocument();
  //     expect(container.querySelector('.capabilities-container')).toBeInTheDocument();
  //   });

  //   it('matches snapshot', () => {
  //     const { container } = renderHome();
  //     expect(container).toMatchSnapshot();
  //   });
  // });
});