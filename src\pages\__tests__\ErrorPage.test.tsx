import { render, screen } from "@testing-library/react";
import ErrorPage from "../ErrorPage";
import { vi } from "vitest";
import { act } from "react";

// Update the initial mock setup
const mockNavigate = vi.fn();
const mockUseLocation = vi.fn();
const mockUseRouteError = vi.fn();

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockUseLocation(),
    useRouteError: () => mockUseRouteError(),
  };
});

describe("ErrorPage", () => {
  it("should render PageErrorFallback with error message", () => {
    const error = new Error("Test error");
    mockUseRouteError.mockReturnValue(error);
    render(<ErrorPage />);
    expect(screen.getByText("Something went wrong")).toBeInTheDocument();
    expect(screen.getByText("Test error")).toBeInTheDocument();
  });

  it("should navigate to the root when reset button is clicked", () => {
    const error = new Error("Test error");
    mockUseRouteError.mockReturnValue(error);
    render(<ErrorPage />);
    act(() => {
      screen.getByText("Try again").click();
    });
    expect(mockNavigate).toHaveBeenCalledWith(0);
  });
});
