// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Home Component > Layout and Styling > matches snapshot 1`] = `
<div>
  <div
    class="_home-container_fbc146"
  >
    <div
      class="_header_fbc146"
    >
      <div
        class="_left-section_fbc146"
      >
        <h1
          class="_heading_fbc146"
        >
          NPDDC
        </h1>
        <p
          class="_description_fbc146"
        >
          Nutro Product Distributor Data Center’s current approach allows the admin to track and monitor the distributor data, the business to edit to the blob store and navigate within the filesystem, which poses a security risk, especially in the production environment. NPDDC allows users to map and attribute data for US and Canada Mars Pet distributor data sources.
        </p>
      </div>
      <div
        class="_right-section_fbc146"
      >
        <img
          alt="Mars Logo"
          class="_logo_fbc146"
          src="/src/assets/mars-petcare.png"
        />
      </div>
    </div>
  </div>
  <h2
    class="_app-capabilities_fbc146"
  >
    App Capabilities
  </h2>
  <div
    class="_capabilities-container_fbc146"
  >
    <div
      data-route="/store-search"
      data-testid="capability-card"
    >
      Store
      -card
    </div>
    <div
      data-route="/product"
      data-testid="capability-card"
    >
      Product
      -card
    </div>
    <div
      data-route="/attribute-list"
      data-testid="capability-card"
    >
      Admin
      -card
    </div>
  </div>
</div>
`;
