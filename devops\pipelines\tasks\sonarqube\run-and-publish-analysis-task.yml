parameters:
  - name: sonarqubeInstance
    type: string
    default: ''
  - name: project<PERSON>ey
    type: string
    default: ''
  - name: projectName
    type: string
    default: ''

steps:
- ${{ if eq(parameters.sonarqubeInstance, '') }}:    
  - script: |
      echo "##vso[task.logissue type=warning]The 'sonarqubeInstance' parameter is not set. SonarQube analysis will be skipped."
      echo "##vso[task.complete result=SucceededWithIssues;]DONE"
    displayName: 'Display Warning: SonarQube parameters were not provided'

- ${{ if ne(parameters.sonarqubeInstance, '') }}:
  - task: SonarQubePrepare@7
    displayName: 'Prepare analysis on SonarQube'
    inputs:
      SonarQube: ${{parameters.sonarqubeInstance}}
      scannerMode: CLI
      configMode: manual
      cliProjectKey: ${{parameters.projectKey}}
      cliProjectName: ${{parameters.projectName}}
  - task: SonarQubeAnalyze@7
    displayName: 'Run Code Analysis'
  - task: SonarQubePublish@7
    displayName: 'Publish Quality Gate Result'