import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ConfirmationDialog from '../ConfirmationDialog'; // adjust path as needed

describe('ConfirmationDialog', () => {
  const defaultProps = {
    open: true,
    title: 'Delete Item',
    message: 'Are you sure you want to delete this item?',
    onCancel: vi.fn(),
    onConfirm: vi.fn(),
    confirmText: 'Yes, Delete',
    cancelText: 'No, Cancel',
  };

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the dialog with title and message', () => {
    render(<ConfirmationDialog {...defaultProps} />);
    expect(screen.getByText('Delete Item')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to delete this item?')).toBeInTheDocument();
  });

  it('renders custom confirm and cancel button text', () => {
    render(<ConfirmationDialog {...defaultProps} />);
    expect(screen.getByText('Yes, Delete')).toBeInTheDocument();
    expect(screen.getByText('No, Cancel')).toBeInTheDocument();
  });

  it('calls onCancel when cancel button is clicked', () => {
    render(<ConfirmationDialog {...defaultProps} />);
    fireEvent.click(screen.getByText('No, Cancel'));
    expect(defaultProps.onCancel).toHaveBeenCalled();
  });

  it('calls onConfirm when confirm button is clicked', () => {
    render(<ConfirmationDialog {...defaultProps} />);
    fireEvent.click(screen.getByText('Yes, Delete'));
    expect(defaultProps.onConfirm).toHaveBeenCalled();
  });

  it('does not render when open is false', () => {
    render(<ConfirmationDialog {...defaultProps} open={false} />);
    expect(screen.queryByText('Delete Item')).not.toBeInTheDocument();
  });

  it('renders default button text when custom text not provided', () => {
    render(
      <ConfirmationDialog
        {...defaultProps}
        confirmText={undefined}
        cancelText={undefined}
      />
    );
    expect(screen.getByText('Confirm')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });
});
