import { describe, it, vi, beforeEach, expect } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import store from '../../store';
import * as attributeActions from '../../services/action/attribute.action';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import AttributeList from '../admin/AttributeList';

// Mock toast notifications
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));


const renderWithProviders = (ui: React.ReactElement) => {
  const container = document.createElement('div');
  document.body.appendChild(container);
  return render(
    <Provider store={store}>
      <ThemeProvider theme={createTheme()}>{ui}</ThemeProvider>
    </Provider>,
    { container }
  );
};

describe('AttributeList Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders layout with Add New button', () => {
    renderWithProviders(<AttributeList />);
    expect(screen.getByText(/Add New/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument();
  });


  it('calls getAttributeListItems on mount', async () => {
    const spy = vi.spyOn(attributeActions, 'getAttributeListItems');

    renderWithProviders(<AttributeList />);

    await waitFor(() => {
      expect(spy).toHaveBeenCalled();
    });
  });
});
