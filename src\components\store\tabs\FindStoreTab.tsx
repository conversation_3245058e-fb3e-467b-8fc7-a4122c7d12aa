// components/FindStoreTab.tsx
import React, { useState, useCallback, useEffect } from "react";
import {
  Box,
  TextField,
  Button,
  Grid,
  Typography,
  Paper,
  Divider,
  CircularProgress,
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { findStores } from "../../../services/action/store.action";
import TableComponent from "../../TableComponent";
import { debounce } from "@mui/material/utils";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";
import { resetSearchResults } from "../../../store/slice/storeSlice";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

interface FindStoreTabProps {
  storeId: string;
  onMappedToClick: (nutroId: string) => void;
  onStoreSelect: (store: { VCID: number; NutrostoreID: number }) => void;
  selectedStore: { VCID: number; NutrostoreID: number } | null;
}

const FindStoreTab: React.FC<FindStoreTabProps> = ({
  storeId,
  onMappedToClick,
  onStoreSelect,
  selectedStore,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const { searchResults, searchTotalCount, isSearching, searchError } = useSelector(
    (state: RootState) => state.store,
  );

  const [filters, setFilters] = useState({
    NutroStoreID: 0,
    ChainName: "",
    StoreName: "",
    StoreNumber: "",
    Address: "",
    City: "",
    State: "",
    ZipCode: "",
    Phone: "",
  });

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [isFilterApplied, setIsFilterApplied] = useState(false);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  const fetchStores = useCallback(() => {
    // Only include filters that have values
    const params: any = {
      page: pagination.page,
      limit: pagination.rowsPerPage,
    };
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params[key] = value;
    });

    dispatch(findStores(params));
  }, [dispatch, filters, pagination.page, pagination.rowsPerPage]);

  const handleSearch = useCallback(
    debounce(() => {
      setPagination((prev) => ({ ...prev, page: 1 }));
      setIsFilterApplied(true);
      fetchStores();
    }, 500),
    [fetchStores],
  );

  const handleReset = () => {
    setFilters({
      NutroStoreID: 0,
      ChainName: "",
      StoreName: "",
      StoreNumber: "",
      Address: "",
      City: "",
      State: "",
      ZipCode: "",
      Phone: "",
    });
    dispatch(resetSearchResults());
    setIsFilterApplied(false);
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });

    if (isFilterApplied) {
      dispatch(
        findStores({
          page: newPage,
          limit: newRowsPerPage,
          ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value)),
        }),
      );
    }
  };

  const columns = [
    {
      id: "action",
      label: "Action",
      description: "Mapping actions",
      format: (value: string, row: any) => (
        <Button
          variant={selectedStore?.NutrostoreID === row.NutroStoreID ? "contained" : "outlined"}
          size="small"
          onClick={() =>
            onStoreSelect({
              VCID: parseInt(storeId),
              NutrostoreID: parseInt(row.NutroStoreID),
            })
          }
          sx={{
            textTransform: "none",
            minWidth: "80px",
          }}
        >
          {selectedStore?.NutrostoreID === row.NutroStoreID ? "Selected" : "Select"}
        </Button>
      ),
    },
    {
      id: "NutroStoreID",
      label: "Nutro ID",
      description: "Unique store identifier",
      format: (value: string, row: any) => (
        <Box
          component="span"
          sx={{
            color: "primary.main",
            cursor: "pointer",
            "&:hover": {
              textDecoration: "underline",
            },
          }}
          onClick={() => onMappedToClick(value)}
        >
          {value}
        </Box>
      ),
    },
    { id: "ChainName", label: "Chain Name", description: "Store chain name" },
    { id: "StoreName", label: "Store Name", description: "Store location name" },
    { id: "StoreNumber", label: "Store Number", description: "Store number" },
    { id: "Address", label: "Address", description: "Street address" },
    { id: "City", label: "City", description: "City" },
    { id: "State", label: "State", description: "State" },
    { id: "ZipCode", label: "Zip Code", description: "Postal code" },
    { id: "Phone", label: "Phone", description: "Phone number" },
  ];

  return (
    <Box>
      <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Search Stores
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="NutroStoreID"
              label="Nutro Store ID"
              value={filters.NutroStoreID}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="ChainName"
              label="Chain Name"
              value={filters.ChainName}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="StoreName"
              label="Store Name"
              value={filters.StoreName}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="StoreNumber"
              label="Store Number"
              value={filters.StoreNumber}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="Address"
              label="Address"
              value={filters.Address}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="City"
              label="City"
              value={filters.City}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="State"
              label="State"
              value={filters.State}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="ZipCode"
              label="Zip Code"
              value={filters.ZipCode}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              name="Phone"
              label="Phone"
              value={filters.Phone}
              onChange={handleFilterChange}
              fullWidth
              size="small"
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: "flex", gap: 2 }}>
          <Button
            variant="contained"
            onClick={handleSearch}
            disabled={isSearching}
            startIcon={isSearching ? <CircularProgress size={20} /> : null}
          >
            {isSearching ? "Searching..." : "Search"}
          </Button>
          <Button variant="outlined" onClick={handleReset} disabled={isSearching}>
            Reset
          </Button>
        </Box>
      </Paper>

      {searchError && (
        <Box sx={{ mb: 2 }}>
          <Typography color="error">{searchError}</Typography>
        </Box>
      )}

      {isSearching && searchResults.length === 0 ? (
        <Box sx={{ display: "flex", justifyContent: "center", p: 4 }}>
          <CircularProgress />
        </Box>
      ) : isFilterApplied && searchResults.length > 0 ? (
        <>
          <TableComponent
            columns={columns}
            rows={searchResults}
            rowsPerPage={pagination.rowsPerPage}
            page={pagination.page}
            totalCount={searchTotalCount}
            onPageChange={handlePageChange}
            showActions={false}
            isLoading={isSearching}
          />

          <Typography variant="body2" sx={{ textAlign: "center", mt: 1 }}>
            Showing {(pagination.page - 1) * pagination.rowsPerPage + 1} to{" "}
            {Math.min(pagination.page * pagination.rowsPerPage, searchTotalCount)} of{" "}
            {searchTotalCount} stores
          </Typography>
        </>
      ) : (
        isFilterApplied &&
        searchResults.length === 0 && (
          <Box sx={{ p: 3, textAlign: "center" }}>
            <Typography variant="body1">
              No stores found. Try adjusting your search filters.
            </Typography>
          </Box>
        )
      )}
    </Box>
  );
};

export default FindStoreTab;
