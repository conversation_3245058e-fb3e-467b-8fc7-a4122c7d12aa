import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import CapabilityCard from '../Card';
import { useNavigate } from 'react-router-dom';
import type { CapabilityCardProps } from '../Card';

// Mock react-router-dom's useNavigate with type safety
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

// Mock MUI icons with proper typing
vi.mock('@mui/icons-material/ArrowCircleRight', () => ({
  default: (props: any) => <div data-testid="arrow-icon">ArrowCircleRightIcon</div>,
}));

vi.mock('@mui/icons-material/InfoOutlined', () => ({
  default: (props: any) => <div data-testid="info-icon">InfoOutlinedIcon</div>,
}));

// Mock Mars components with enhanced type safety
vi.mock('@mars/vizx-react', () => ({
  Card: ({ children, onClick, className }: { 
    children: React.ReactNode; 
    onClick: () => void;
    className?: string;
  }) => (
    <div 
      onClick={onClick} 
      data-testid="card" 
      className={className}
    >
      {children}
    </div>
  ),
  Tooltip: ({ children, title }: { 
    children: React.ReactNode; 
    title: string;
  }) => (
    <div data-testid="tooltip" data-tooltip-title={title}>
      {children}
    </div>
  ),
}));

describe('CapabilityCard Component', () => {
  const mockNavigate = vi.fn();
  const defaultProps: CapabilityCardProps = {
    title: 'Test Card',
    logo: 'test-logo.png',
    route: '/test-route',
  };
  
  const customProps: CapabilityCardProps = {
    ...defaultProps,
    tooltipText: 'Custom tooltip text',
    className: 'custom-class'
  };

  beforeEach(() => {
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders basic content correctly', () => {
      render(<CapabilityCard {...defaultProps} />);
      
      expect(screen.getByText(defaultProps.title)).toBeInTheDocument();
      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('arrow-icon')).toBeInTheDocument();
      expect(screen.getByTestId('info-icon')).toBeInTheDocument();
      
      const img = screen.getByRole('img');
      expect(img).toHaveAttribute('src', defaultProps.logo);
      expect(img).toHaveAttribute('alt', `${defaultProps.title} Logo`);
    });

    it('renders with default tooltip text when not provided', () => {
      render(<CapabilityCard {...defaultProps} />);
      
      const tooltip = screen.getByTestId('tooltip');
      expect(tooltip).toHaveAttribute('data-tooltip-title', 'Click to view more');
    });

    it('renders with custom tooltip text when provided', () => {
      render(<CapabilityCard {...customProps} />);
      
      const tooltip = screen.getByTestId('tooltip');
      expect(tooltip).toHaveAttribute('data-tooltip-title', customProps.tooltipText);
    });

    it('applies custom className when provided', () => {
      render(<CapabilityCard {...customProps} />);
      
      const card = screen.getByTestId('card');
      expect(card).toHaveClass(customProps.className!);
    });
  });

  describe('Interactions', () => {
    it('navigates to correct route when clicked', () => {
      render(<CapabilityCard {...defaultProps} />);
      
      fireEvent.click(screen.getByTestId('card'));
      expect(mockNavigate).toHaveBeenCalledWith(defaultProps.route);
    });
  });
});