import React, { useState } from "react";
import { Button, Menu, MenuItem } from "@mars/vizx-react";
import { useNavigate } from "react-router-dom";

interface MenuItemWithDropdownProps {
  label: string;
  menuItems: { id: string; title: string }[];
  selectedMenuItem: string;
}

const MenuItemWithDropdown: React.FC<MenuItemWithDropdownProps> = ({
  label,
  menuItems,
  selectedMenuItem,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const isMenuOpen = Boolean(anchorEl);
  const navigate = useNavigate();

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (path: string) => {
    navigate(path);
    handleClose();
  };

  return (
    <div>
      <Button
        variant="text"
        onClick={handleClick}
        style={{
          color:
            menuItems.some((item) => selectedMenuItem.startsWith(item.id)) 
              ? "white" 
              : "#fff",
          borderBottom:
            menuItems.some((item) => selectedMenuItem.startsWith(item.id))
              ? "2px solid white"
              : "none",
              padding: "8px 50px", 
          fontSize: "0.875rem", 
          fontWeight: 500,
          textTransform: "none", 
        }}
      >
        {label} ▾
      </Button>

      <Menu anchorEl={anchorEl} open={isMenuOpen} onClose={handleClose}>
        {menuItems.map((item) => (
          <MenuItem key={item.id} onClick={() => handleMenuItemClick(item.id)}>
            {item.title}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};

export default MenuItemWithDropdown;
