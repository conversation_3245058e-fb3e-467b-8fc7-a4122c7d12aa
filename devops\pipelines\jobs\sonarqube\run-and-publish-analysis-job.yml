parameters:
  - name: sonarqubeInstance
    type: string
    default: ''
  - name: project<PERSON><PERSON>
    type: string
    default: ''
  - name: projectName
    type: string
    default: ''

jobs:
- job: Sonarqube_Analysis
  displayName: SonarqubeAnalysis
  steps:
  - checkout: self
  - template: ../../tasks/sonarqube/run-and-publish-analysis-task.yml
    parameters:
      sonarqubeInstance: ${{ parameters.sonarqubeInstance }}
      projectKey: ${{ parameters.projectKey }}
      projectName: ${{ parameters.projectName }}