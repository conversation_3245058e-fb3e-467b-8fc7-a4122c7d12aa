import React from "react";
import { Card, Tooltip } from "@mars/vizx-react";
import { useNavigate } from "react-router-dom";
import ArrowCircleRightIcon from "@mui/icons-material/ArrowCircleRight";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import styles from "../styles/CardComponent.module.css";

interface CapabilityCardProps {
  logo: string;
  title: string;
  route: string;
  tooltipText?: string;
  className?: string;
}

const CapabilityCard: React.FC<CapabilityCardProps> = ({
  logo,
  title,
  route,
  tooltipText = "Click to view more",
  className = ""
}) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate(route);
  };

  return (
    <Card 
      onClick={handleClick} 
      className={`${styles["card-container"]} ${className}`}
    >
      {/* Info Tooltip */}
      <div className={styles["info-icon"]}>
        <Tooltip title={tooltipText} arrow>
          <div className="rounded-full bg-gray-200 p-1 hover:bg-gray-300 cursor-pointer">
            <InfoOutlinedIcon fontSize="small" />
          </div>
        </Tooltip>
      </div>

      {/* Logo with Perfect Circle Background */}
      <div className={styles["logo-circle-container"]}>
        <div className={styles["logo-background"]} />
        <img
          src={logo}
          alt={`${title} Logo`}
          className={styles["card-logo"]}
          onError={(e) => {
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      </div>

      {/* Title with Animated Arrow */}
      <div className="flex items-center justify-center gap-2">
        <h3 className={styles["card-title"]}>{title}</h3>
        <ArrowCircleRightIcon 
          fontSize="small" 
          className={styles["arrow-icon"]} 
        />
      </div>
    </Card>
  );
};

export default CapabilityCard;