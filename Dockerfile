FROM node:22-alpine AS builder
WORKDIR /app

ARG NPM_TOKEN
ARG EFFEM_EMAIL

RUN npm config set userconfig /npm_config/.npmrc
RUN npm config set strict-ssl false
RUN npm config set @mars:registry https://pkgs.dev.azure.com/marsanalytics/_packaging/mars-react-components/npm/registry/
RUN npm config set //pkgs.dev.azure.com/marsanalytics/_packaging/mars-react-components/npm/registry/:username marsanalytics
RUN npm config set //pkgs.dev.azure.com/marsanalytics/_packaging/mars-react-components/npm/registry/:_password ${NPM_TOKEN}
RUN npm config set //pkgs.dev.azure.com/marsanalytics/_packaging/mars-react-components/npm/registry/:email ${NPM_TOKEN}
RUN npm config set //pkgs.dev.azure.com/marsanalytics/_packaging/mars-react-components/npm/:username marsanalytics
RUN npm config set //pkgs.dev.azure.com/marsanalytics/_packaging/mars-react-components/npm/:_password ${NPM_TOKEN}
RUN npm config set //pkgs.dev.azure.com/marsanalytics/_packaging/mars-react-components/npm/:email ${EFFEM_EMAIL}


COPY package.json .
RUN npm cache clean --force
RUN rm -rf node_modules
RUN npm install -g npm@9.1.3
RUN npm install --legacy-peer-deps

COPY . .

RUN npm run build

RUN ls -alh /app/dist

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
RUN chown nginx:nginx /usr/share/nginx/html/*

# Copy the build files from the previous stage
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port 80
EXPOSE 80

#Expose port 8181
#EXPOSE 8181

#Expose port 8000
EXPOSE 8000


CMD ["nginx", "-g", "daemon off;"]