import React, { useEffect, useState, useCallback } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { useDispatch, useSelector } from "react-redux";
import AddIcon from "@mui/icons-material/Add";
import { RootState } from "../../store";
import { setUserFilters, resetUserFilters, setCurrentUser } from "../../store/slice/adminSlice";
import {
  getUsers,
  createUser,
  updateUser,
  deleteUser,
  getAllRoles,
  getAllManagers,
  assignUserRole,
} from "../../services/action/user.action";
import { Box, Button } from "@mui/material";
import { useNavigate } from "react-router-dom";
import UserModal from "../../components/UserModal";
import FilterRow from "../../components/FilterRow";
import { toast } from "react-toastify";
import { UnknownAction } from "@reduxjs/toolkit";
import { ThunkDispatch } from "redux-thunk";
import { User } from "../../types/admin.types";
import { debounce } from "@mui/material/utils";
import ConfirmationDialog from "../../components/ConfirmationDialog";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

const userTypes = [
  { value: "Customer", label: "Customer" },
  { value: "NPDDC", label: "NPDDC" },
  { value: "Partner", label: "Partner" },
];

const activeOptions = [
  { value: "True", label: "Active" },
  { value: "False", label: "Inactive" },
];

const UserList = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();
  const dispatch: AppDispatch = useDispatch();
  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 20,
  });

  const {
    users,
    isLoading,
    currentUser,
    roles,
    totalCountUsers,
    filters: {
      userFilters: { active, userType },
    },
  } = useSelector((state: RootState) => state.admin);

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  useEffect(() => {
    dispatch(getAllRoles());
    dispatch(getAllManagers());
  }, [dispatch]);

  const fetchUsers = useCallback(() => {
    dispatch(
      getUsers({
        limit: pagination.rowsPerPage,
        page: pagination.page,
        active: active?.value || undefined,
        user_type: userType?.value || undefined,
        search_query: searchQuery,
      }),
    );
  }, [dispatch, active, userType, searchQuery, pagination.page, pagination.rowsPerPage]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const handleSearch = useCallback(
    debounce((query: string) => {
      setSearchQuery(query);
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page on new search
    }, 500),
    [],
  );

  const handleEdit = (row: User) => {
    setModalMode("edit");
    dispatch(setCurrentUser(row));
    setModalOpen(true);
  };

  const handleDeleteClick = (row: User) => {
    setUserToDelete(row);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!userToDelete) return;

    try {
      await dispatch(deleteUser(userToDelete.UserID as string)).unwrap();
      toast.success("User deleted successfully");
      fetchUsers();
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(error.message || "Failed to delete user");
      } else {
        toast.error("Failed to delete user");
      }
    } finally {
      setDeleteDialogOpen(false);
      setUserToDelete(null);
    }
  };

  const handleAddNew = () => {
    navigate("/new-user");
  };

  const handleSaveUser = async (user: User) => {
    try {
      if (modalMode === "add") {
        await dispatch(createUser(user)).unwrap();
        toast.success("User created successfully");
      } else {
        const response = await dispatch(
          updateUser({
            id: currentUser.UserID as string,
            data: {
              ...user,
              UserFullName: `${user.UserFirstName} ${user.UserLastName}`.trim(),
            },
          }),
        ).unwrap();

        if (currentUser.UserID && user.RoleID) {
          await dispatch(
            assignUserRole({
              userId: currentUser.UserID,
              roleId: user.RoleID,
            }),
          ).unwrap();
        }
        toast.success("User updated successfully");
      }
      setModalOpen(false);
      fetchUsers();
    } catch (error) {
      const errorMessage = (() => {
        if (typeof error === "string") return error;
        if (error && typeof error === "object") {
          if ("response" in error && error.response?.data?.message) {
            return error.response.data.message;
          }
          if ("message" in error) return error.message;
          return JSON.stringify(error);
        }
        return "Unknown error";
      })();

      if (errorMessage.includes("409")) {
        toast.error("User with this Username or Email already exists");
      } else {
        toast.error(`Failed to ${modalMode} user`);
      }
    }
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const processedRows = users.map((user) => ({
    ...user,
    rolesDisplay: user.roles?.map((role) => role.RoleName).join(", ") || "-",
  }));

  const columns = [
    { id: "UserLogin", label: "Username", minWidth: 120 },
    {
      id: "UserFullName",
      label: "Full Name",
      minWidth: 100,
      format: (value: string) => value || "-",
    },
    { id: "UserEmail", label: "Email", minWidth: 180 },
    {
      id: "rolesDisplay",
      label: "Role",
      minWidth: 120,
      format: (value: string) => value || "-",
    },
    {
      id: "UserType",
      label: "Type",
      minWidth: 100,
      format: (value: string) => value || "-",
    },
    {
      id: "MarsUsername",
      label: "Mars ID",
      minWidth: 100,
      format: (value: string) => value || "-",
    },
    {
      id: "Active",
      label: "Status",
      minWidth: 100,
      format: (value: boolean) => (
        <span
          style={{
            backgroundColor: value ? "#4caf50" : "#f44336",
            color: "white",
            padding: "4px 8px",
            borderRadius: "12px",
            fontSize: "0.75rem",
            fontWeight: "bold",
          }}
        >
          {value ? "Active" : "Inactive"}
        </span>
      ),
    },
    {
      id: "IsLockedOut",
      label: "Locked",
      minWidth: 80,
      format: (value: boolean) => (value ? "Yes" : "No"),
      style: (value: boolean) => ({
        color: value ? "red" : "green",
      }),
    },
  ];

  const handleResetFilters = () => {
    setSearchQuery("");
    setPagination((prev) => ({ ...prev, page: 1 }));
    dispatch(resetUserFilters());
  };

  return (
    <div>
      <TopBarLayout
        breadcrumbItems={["Admin", "Users"]}
        onSearchChange={(value) => {
          setSearchQuery(value);
          handleSearch(value);
        }}
      />

      <FilterRow
        filters={[
          {
            name: "active",
            label: "Status",
            type: "select",
            options: activeOptions,
            value: active?.value || "",
            onChange: (value) => {
              const selectedOption = activeOptions.find((opt) => opt.value === value);
              dispatch(
                setUserFilters({
                  active: selectedOption || null,
                }),
              );
              setPagination((prev) => ({ ...prev, page: 1 }));
            },
          },
          {
            name: "userType",
            label: "User Type",
            options: userTypes,
            type: "select",
            value: userType?.value || "",
            onChange: (value) => {
              const selectedOption = userTypes.find((opt) => opt.value === value);
              dispatch(
                setUserFilters({
                  userType: selectedOption || null,
                }),
              );
              setPagination((prev) => ({ ...prev, page: 1 }));
            },
          },
        ]}
        onReset={handleResetFilters}
        showActionButtons={false}
      />

      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          p: 1,
        }}
      >
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddNew}
          disabled={isLoading}
          sx={{
            backgroundColor: "#00008B",
            "&:hover": { backgroundColor: "#000070" },
          }}
        >
          Add New
        </Button>
      </Box>

      <TableComponent
        columns={columns}
        rows={processedRows}
        onEdit={handleEdit}
        onDelete={handleDeleteClick}
        rowsPerPage={pagination.rowsPerPage}
        page={pagination.page}
        totalCount={totalCountUsers || 0}
        onPageChange={handlePageChange}
        isLoading={isLoading}
      />

      <UserModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSave={handleSaveUser}
        mode={modalMode}
        initialData={currentUser}
      />
      <ConfirmationDialog
        open={deleteDialogOpen}
        title="Confirm Deletion"
        message={`Are you sure you want to delete user ${userToDelete?.UserLogin}? This action cannot be undone.`}
        onCancel={() => setDeleteDialogOpen(false)}
        onConfirm={handleDeleteConfirm}
        confirmText="Delete"
        cancelText="Cancel"
      />
    </div>
  );
};

export default UserList;
