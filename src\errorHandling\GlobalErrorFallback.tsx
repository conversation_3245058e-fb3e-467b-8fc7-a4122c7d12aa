import { PageErrorFallback } from "@mars/vizx-react";
import PropTypes from "prop-types";

export type GlobalErrorFallbackProps = {
  error: unknown;
};

export const GlobalErrorFallback: React.FC<GlobalErrorFallbackProps> = ({ error }) => {
  return (
    <PageErrorFallback
      error={error}
      resetErrorBoundary={() => {
        location.reload();
      }}
    />
  );
};

GlobalErrorFallback.propTypes = {
  error: PropTypes.any,
};

GlobalErrorFallback.displayName = "GlobalErrorFallback";
