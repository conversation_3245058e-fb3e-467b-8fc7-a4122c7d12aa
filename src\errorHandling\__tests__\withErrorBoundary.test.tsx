import { render, screen } from "@testing-library/react";
import { WithErrorBoundary, WithPageErrorBoundary } from "../withErrorBoundary";

const TestComponent = () => <div>Test Component</div>;

const ErrorThrowingComponent = () => {
  throw new Error("Test error");
};

describe("WithErrorBoundary", () => {
  it("should render the component without error", () => {
    const WrappedComponent = () => WithErrorBoundary(TestComponent);
    render(<WrappedComponent />);
    expect(screen.getByText("Test Component")).toBeInTheDocument();
  });

  it("should render the fallback component when an error is thrown", () => {
    const WrappedComponent = () => WithErrorBoundary(ErrorThrowingComponent);
    render(<WrappedComponent />);
    expect(screen.getByText("Something went wrong")).toBeInTheDocument();
  });

  it("should use custom fallback component when provided", () => {
    const CustomFallback = ({ error }: { error: Error }) => (
      <div>Custom Fallback: {error.message}</div>
    );
    const WrappedComponent = () => WithErrorBoundary(ErrorThrowingComponent, CustomFallback);
    render(<WrappedComponent />);
    expect(screen.getByText("Custom Fallback: Test error")).toBeInTheDocument();
  });

  it("should call onReset when reset button is clicked", () => {
    const WrappedComponent = () => WithErrorBoundary(ErrorThrowingComponent);
    render(<WrappedComponent />);
    screen.getByText("Try again").click();
    // Add assertions for onReset behavior if needed
  });
});

describe("WithPageErrorBoundary", () => {
  it("should render the component without error", () => {
    const WrappedComponent = () => WithPageErrorBoundary(TestComponent);
    render(<WrappedComponent />);
    expect(screen.getByText("Test Component")).toBeInTheDocument();
  });

  it("should render the page error fallback component when an error is thrown", () => {
    const WrappedComponent = () => WithPageErrorBoundary(ErrorThrowingComponent);
    render(<WrappedComponent />);
    expect(screen.getByText("Something went wrong")).toBeInTheDocument();
  });
});
