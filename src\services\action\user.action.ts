import { createAsyncThunk } from "@reduxjs/toolkit";
import UserService from "../api/user.service";

interface UserParams {
  limit?: number;
  page?: number;
  active?: string;
  search_query?: string;
  user_type?: string;
}

interface User {
  UserID?: string;
  UserLogin: string;
  UserFirstName?: string;
  UserLastName?: string;
  UserFullName?: string;
  UserEmail?: string;
  MarsUsername?: string;
  UserType?: string;
  ManagerID?: string;
  IsLockedOut?: boolean;
  Active?: boolean;
  RoleID?: string;
}

interface Role {
  RoleID: string;
  RoleName: string;
  RoleDescription?: string;
}

interface Manager {
  UserID: string;
  UserLogin: string;
  UserFullName?: string;
}

export const getUsers = createAsyncThunk("user/getUsers", async (params: UserParams) => {
  const response = await UserService.getUsers(params);
  return response;
});

export const getAllRoles = createAsyncThunk("user/getAllRoles", async () => {
  const response = await UserService.getAllRoles();
  return response.data;
});

export const getAllManagers = createAsyncThunk("user/getAllManagers", async () => {
  const response = await UserService.getAllManagers();
  return response.data;
});

export const createUser = createAsyncThunk("user/createUser", async (data: User) => {
  try {
    const response = await UserService.createUser(data);
    console.log("responsee-----", response);
    return response.data;
  } catch (error: any) {
    // Extract the detailed error message
    console.log("error", error.response);
    const errorMessage = error.response?.data?.detail || "Failed to create user";
    throw new Error(errorMessage);
  }
});

export const getUser = createAsyncThunk("user/getUser", async (id: string) => {
  const response = await UserService.getUser(id);
  return response.data;
});

export const updateUser = createAsyncThunk(
  "user/updateUser",
  async ({ id, data }: { id: string; data: User }) => {
    const response = await UserService.updateUser(id, data);
    return response.data;
  },
);

export const patchUser = createAsyncThunk(
  "user/patchUser",
  async ({ id, data }: { id: string; data: Partial<User> }) => {
    const response = await UserService.patchUser(id, data);
    return response.data;
  },
);

export const deleteUser = createAsyncThunk("user/deleteUser", async (id: string) => {
  await UserService.deleteUser(id);
  return id;
});

export const assignUserRole = createAsyncThunk(
  "user/assignUserRole",
  async ({ userId, roleId }: { userId: string; roleId: string }) => {
    const response = await UserService.assignUserRole(userId, roleId);
    return { userId, roleId };
  },
);
