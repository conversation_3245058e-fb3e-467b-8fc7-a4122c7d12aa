import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { AppHeader } from '../AppHeader';
import { useNavigate, useLocation } from 'react-router-dom';
import { Header } from '@mars/vizx-react';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
  useLocation: vi.fn(),
}));

vi.mock('@mars/vizx-react', async () => {
  const actual = await vi.importActual('@mars/vizx-react');
  return {
    ...actual,
    Header: vi.fn(({ logo, onLogoClick }) => (
      <div onClick={onLogoClick} data-testid="header">
        {logo}
      </div>
    )),
  };
});



vi.mock('../assets/mars-petcare.png', () => 'mars-logo-mock');
vi.mock('../MenuBar', () => ({
  default: () => <div data-testid="menu-bar">MenuBar</div>,
}));

describe('AppHeader Component', () => {
  const mockNavigate = vi.fn();
  const mockLocation = { pathname: '/' };

  beforeEach(() => {
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    vi.mocked(useLocation).mockReturnValue(mockLocation);
  });

  it('renders header with logo and title', () => {
    render(<AppHeader />);
    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByAltText('logo')).toBeInTheDocument();
  });

  it('navigates to home on logo click', () => {
    render(<AppHeader />);
    fireEvent.click(screen.getByAltText('logo'));
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

it('renders MenuBar component', () => {
    render(<AppHeader />);
    expect(screen.getByTestId('menu-bar')).toBeInTheDocument();
  });

  it('updates selected menu item when location changes', () => {
    vi.mocked(useLocation).mockReturnValue({ pathname: '/history' });
    render(<AppHeader />);
    expect(Header).toHaveBeenCalledWith(
      expect.objectContaining({
        userMenuItems: expect.arrayContaining([
          expect.objectContaining({ id: '/history' })
        ])
      }),
      expect.anything()
    );
  });
});