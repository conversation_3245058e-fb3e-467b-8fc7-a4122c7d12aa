import { createSlice } from "@reduxjs/toolkit";
import {
  getAttributeListItems,
  createAttributeListItem,
  updateAttributeListItem,
  deleteAttributeListItem,
  getAllListNames,
} from "../../services/action/attribute.action";
import {
  getUsers,
  getAllRoles,
  getAllManagers,
  createUser,
  getUser,
  updateUser,
  deleteUser,
  assignUserRole,
} from "../../services/action/user.action";
import { AdminState } from "../../types/admin.types";

const initialState: AdminState = {
  filters: {
    attributeFilters: {
      attribute: null,
      storeId: "",
    },
    userFilters: {
      active: null,
      userType: null,
    },
  },
  currentAttribute: null,
  currentUser: null,
  listNames: [],
  attributes: [],
  totalCountAttributes: 0,
  totalCountUsers: 0,
  users: [],
  roles: [],
  managers: [],
  isLoading: false,
  error: null,
};

const adminSlice = createSlice({
  name: "admin",
  initialState,
  reducers: {
    setAttributeFilters(state, action) {
      state.filters.attributeFilters = {
        ...state.filters.attributeFilters,
        ...action.payload,
      };
    },
    setUserFilters(state, action) {
      state.filters.userFilters = {
        ...state.filters.userFilters,
        ...action.payload,
      };
    },
    resetAttributeFilters(state) {
      state.filters.attributeFilters = initialState.filters.attributeFilters;
    },
    resetUserFilters(state) {
      state.filters.userFilters = initialState.filters.userFilters;
    },
    setCurrentAttribute(state, action) {
      state.currentAttribute = action.payload;
    },
    setCurrentUser(state, action) {
      state.currentUser = action.payload;
    },
    clearCurrentUser(state) {
      state.currentUser = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Attribute cases (keep existing)
      .addCase(getAttributeListItems.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAttributeListItems.fulfilled, (state, action) => {
        state.isLoading = false;
        // state.attributes = action.payload;
        state.attributes = action.payload.attributes;
        state.totalCountAttributes = action.payload.totalCount;
      })
      .addCase(getAttributeListItems.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch attributes";
      })
      .addCase(getAllListNames.fulfilled, (state, action) => {
        state.listNames = action.payload;
      })
      .addCase(createAttributeListItem.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createAttributeListItem.fulfilled, (state, action) => {
        state.isLoading = false;
        state.attributes.push(action.payload);
      })
      .addCase(createAttributeListItem.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to create attribute";
      })
      .addCase(updateAttributeListItem.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateAttributeListItem.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.attributes.findIndex((attr) => attr.RecID === action.payload.RecID);
        if (index !== -1) {
          state.attributes[index] = action.payload;
        }
      })
      .addCase(updateAttributeListItem.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to update attribute";
      })
      .addCase(deleteAttributeListItem.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteAttributeListItem.fulfilled, (state, action) => {
        state.isLoading = false;
        state.attributes = state.attributes.filter((attr) => attr.RecID !== action.payload);
      })
      .addCase(deleteAttributeListItem.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to delete attribute";
      })
      .addCase(getUsers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = action.payload.users;
        state.totalCountUsers = action.payload.totalCount;
      })
      .addCase(getUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to fetch users";
      })

      // Get All Roles
      .addCase(getAllRoles.fulfilled, (state, action) => {
        state.roles = action.payload;
      })
      // Get All Managers
      .addCase(getAllManagers.fulfilled, (state, action) => {
        state.managers = action.payload;
      })

      // Create User
      .addCase(createUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users.push(action.payload);
      })
      .addCase(createUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to create user";
      })

      // Get Single User
      .addCase(getUser.fulfilled, (state, action) => {
        state.currentUser = action.payload;
      })

      // Update User
      .addCase(updateUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.users.findIndex((user) => user.UserID === action.payload.UserID);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
        if (state.currentUser?.UserID === action.payload.UserID) {
          state.currentUser = action.payload;
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to update user";
      })

      // Delete User
      .addCase(deleteUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = state.users.filter((user) => user.UserID !== action.payload);
        if (state.currentUser?.UserID === action.payload) {
          state.currentUser = null;
        }
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || "Failed to delete user";
      })

      // Assign Role to User
      .addCase(assignUserRole.fulfilled, (state, action) => {
        const { userId, roleId } = action.payload;
        const role = state.roles.find((r) => r.RoleID === roleId);

        // Update users array
        state.users = state.users.map((user) => {
          if (user.UserID === userId) {
            return {
              ...user,
              RoleID: roleId,
              RoleName: role?.RoleName,
            };
          }
          return user;
        });

        // Update current user if it's the one being modified
        if (state.currentUser?.UserID === userId) {
          state.currentUser = {
            ...state.currentUser,
            RoleID: roleId,
            RoleName: role?.RoleName,
          };
        }
      });
  },
});

export const {
  setAttributeFilters,
  setUserFilters,
  resetAttributeFilters,
  resetUserFilters,
  setCurrentAttribute,
  setCurrentUser,
  clearCurrentUser,
} = adminSlice.actions;

export default adminSlice.reducer;
