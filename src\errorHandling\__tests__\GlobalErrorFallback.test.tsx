import { render, screen } from "@testing-library/react";
import { GlobalErrorFallback } from "../GlobalErrorFallback";
import { PageErrorFallbackProps } from "@mars/vizx-react";
import { vi } from "vitest";

vi.mock("@mars/vizx-react", () => ({
  PageErrorFallback: ({ error, resetErrorBoundary }: PageErrorFallbackProps) => (
    <div>
      <div>Page Error Fallback</div>
      <div>{error?.toString()}</div>
      <button onClick={resetErrorBoundary}>Reset</button>
    </div>
  ),
}));

describe("GlobalErrorFallback", () => {
  it("should render PageErrorFallback with error message", () => {
    const error = new Error("Test error");
    render(<GlobalErrorFallback error={error} />);
    expect(screen.getByText("Page Error Fallback")).toBeInTheDocument();
    expect(screen.getByText("Error: Test error")).toBeInTheDocument();
  });

  it("should reload the page when reset button is clicked", () => {
    const error = new Error("Test error");
    render(<GlobalErrorFallback error={error} />);

    const reloadMock = vi.fn();
    vi.stubGlobal("location", { reload: reloadMock });

    screen.getByText("Reset").click();
    expect(reloadMock).toHaveBeenCalled();
  });
});
