import React from "react";
import { Box } from "@mui/material";
import TableComponent from "../../TableComponent";
import styles from "../../../styles/Tabs.module.css";

interface DistStoreTabProps {
  storeId: string;
  onMappedToClick: (nutroId: string) => void;
  distStores: any[];
  distStoresTotalCount: number;
  isLoading: boolean;
  pagination: {
    page: number;
    rowsPerPage: number;
  };
  onPageChange: (newPage: number, newRowsPerPage: number) => void;
}

const DistStoreTab: React.FC<DistStoreTabProps> = ({
  storeId,
  onMappedToClick,
  distStores,
  distStoresTotalCount,
  isLoading,
  pagination,
  onPageChange,
}) => {
  const columns = [
    {
      id: "DataSource",
      label: "Data Source",
      description: "Name of the external data source",
    },
    {
      id: "StoreID",
      label: "Store ID",
      description: "ID in the external system",
    },
    {
      id: "StoreDesc",
      label: "Description",
      description: "Description of the store",
    },
    {
      id: "CityStateZip",
      label: "City/State/ZIP",
      description: "Location information",
    },
    {
      id: "FirstDt",
      label: "First DT",
      description: "First data transfer date",
    },
    {
      id: "LastDt",
      label: "Last DT",
      description: "Last data transfer date",
    },
    // {
    //   id: "MappedTo",
    //   label: "Mapped To",
    //   description: "Nutro ID this store is mapped to",
    //   format: (value: string) => (
    //     <Box
    //       component="span"
    //       sx={{
    //         color: "primary.main",
    //         textDecoration: "underline",
    //         cursor: "pointer",
    //         "&:hover": {
    //           color: "primary.dark",
    //           fontWeight: "500",
    //         },
    //       }}
    //       onClick={() => value && onMappedToClick(value)}
    //     >
    //       {value || "-"}
    //     </Box>
    //   ),
    // },
    {
      id: "IsPending",
      label: "Status",
      description: "Current status of the mapping",
      format: (value: boolean) => (
        <Box
          sx={{
            color: value ? "success.main" : "error.main",
            fontWeight: "bold",
          }}
        >
          {value ? "True" : "False"}
        </Box>
      ),
    },
  ];

  return (
    <Box className={styles.tabContent}>
      <TableComponent
        columns={columns}
        rows={distStores}
        rowsPerPage={pagination.rowsPerPage}
        page={pagination.page}
        totalCount={distStoresTotalCount}
        onPageChange={onPageChange}
        showActions={false}
        isLoading={isLoading}
      />
    </Box>
  );
};

export default DistStoreTab;
