import { logError } from "../logError";
import { vi } from "vitest";

describe("logError", () => {
  it("should log error and component stack to console", () => {
    const error = new Error("Test error");
    const info = { componentStack: "Test component stack" };

    const consoleErrorSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    logError(error, info);

    expect(consoleErrorSpy).toHaveBeenCalledWith(error, info.componentStack);

    consoleErrorSpy.mockRestore();
  });
});
