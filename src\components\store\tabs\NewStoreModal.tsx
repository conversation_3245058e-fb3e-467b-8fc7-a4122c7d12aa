import React, { useState } from "react";
import {
  Modal,
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Divider,
  SelectChangeEvent,
} from "@mui/material";
import { useDispatch } from "react-redux";
import { createStore } from "../../../services/action/store.action";
import styles from "../../../styles/NewStoreModal.module.css";
import { useAppDispatch, useAppSelector } from "../../../services/hooks/store.hooks";

interface NewStoreModalProps {
  open: boolean;
  onClose: () => void;
  storeInfo?: {
    address: string;
    country: string;
    phone: string;
    rep: string;
    customerType: string;
    warehouse: string;
    dataSource: string;
  };
}

const NewStoreModal: React.FC<NewStoreModalProps> = ({ open, onClose, storeInfo }) => {
  const dispatch = useDispatch();
  const appDispatch = useAppDispatch();
  const [formData, setFormData] = useState({
    storeNumber: "",
    storeName: "",
    streetAddress: "",
    city: "",
    state: "",
    country: "",
    phone: "",
    locationType: "",
    accountType: "",
  });

  const handleChange = (
    e:
      | React.ChangeEvent<HTMLInputElement>
      | SelectChangeEvent<string>
      | { target: { name?: string; value: unknown } },
  ) => {
    const { name, value } = e.target;
    if (name) {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async () => {
    const storeData = {
      StoreName: formData.storeName,
      StoreNumber: formData.storeNumber,
      LocationAddress: formData.streetAddress,
      LocationCity: formData.city,
      LocationState: formData.state,
      LocationCountry: formData.country,
      Phone: formData.phone,
      LocationType: formData.locationType,
      AccountType: formData.accountType,
      // Default values for required fields in schema
      StoreStatus: "Active",
      ISODate: new Date().toISOString().split("T")[0], // Current date in YYYY-MM-DD format
    };

    await appDispatch(createStore(storeData));
    onClose();
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box className={styles.modalContainer}>
        <Typography variant="h6" className={styles.modalTitle}>
          Create New Store
        </Typography>

        <Grid container spacing={4}>
          {/* Left Column - Form Fields */}
          <Grid item xs={6}>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <TextField
                  name="storeNumber"
                  label="Store Number"
                  fullWidth
                  margin="normal"
                  value={formData.storeNumber}
                  onChange={handleChange}
                  required
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  name="storeName"
                  label="Store Name"
                  fullWidth
                  margin="normal"
                  value={formData.storeName}
                  onChange={handleChange}
                  required
                />
              </Grid>
            </Grid>

            <Typography variant="subtitle1" sx={{ mt: 2, mb: 1 }}>
              Physical Address
            </Typography>

            <TextField
              name="streetAddress"
              label="Street Address"
              fullWidth
              margin="normal"
              value={formData.streetAddress}
              onChange={handleChange}
              required
            />

            <Grid container spacing={2}>
              <Grid item xs={4}>
                <TextField
                  name="city"
                  label="City"
                  fullWidth
                  margin="normal"
                  value={formData.city}
                  onChange={handleChange}
                  required
                />
              </Grid>
              <Grid item xs={4}>
                <TextField
                  name="state"
                  label="State"
                  fullWidth
                  margin="normal"
                  value={formData.state}
                  onChange={handleChange}
                  required
                />
              </Grid>
              <Grid item xs={4}>
                <FormControl fullWidth margin="normal" required>
                  <InputLabel>Country</InputLabel>
                  <Select
                    name="country"
                    value={formData.country}
                    onChange={handleChange}
                    label="Country"
                  >
                    <MenuItem value="">
                      <em>Select Country</em>
                    </MenuItem>
                    <MenuItem value="USA">USA</MenuItem>
                    <MenuItem value="Canada">Canada</MenuItem>
                    <MenuItem value="Others">Others</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <TextField
              name="phone"
              label="Phone"
              fullWidth
              margin="normal"
              value={formData.phone}
              onChange={handleChange}
              required
            />

            {/* <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Location Type</InputLabel>
                  <Select
                    name="locationType"
                    value={formData.locationType}
                    onChange={handleChange}
                    label="Location Type"
                  >
                    <MenuItem value="Retail">Retail</MenuItem>
                    <MenuItem value="Wholesale">Wholesale</MenuItem>
                    <MenuItem value="Online">Online</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Account Type</InputLabel>
                  <Select
                    name="accountType"
                    value={formData.accountType}
                    onChange={handleChange}
                    label="Account Type"
                  >
                    <MenuItem value="Standard">Standard</MenuItem>
                    <MenuItem value="Premium">Premium</MenuItem>
                    <MenuItem value="Enterprise">Enterprise</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid> */}
          </Grid>

          {/* Right Column - Owner Details */}
          <Grid item xs={6}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Owner Details
            </Typography>

            {storeInfo && (
              <Box
                sx={{
                  p: 2,
                  border: "1px solid #e0e0e0",
                  borderRadius: 1,
                  backgroundColor: "#f9f9f9",
                }}
              >
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Data Source:</strong> {storeInfo.dataSource}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Address:</strong> {storeInfo.address}
                </Typography>
                {/* <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>City:</strong> {storeInfo.city}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>State:</strong> {storeInfo.state}
                </Typography> */}
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Country:</strong> {storeInfo.country}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Phone:</strong> {storeInfo.phone}
                </Typography>
                <Divider sx={{ my: 2 }} />
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Rep:</strong> {storeInfo.rep}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Customer Type:</strong> {storeInfo.customerType}
                </Typography>
                <Typography variant="body2">
                  <strong>Warehouse:</strong> {storeInfo.warehouse}
                </Typography>
              </Box>
            )}
          </Grid>
        </Grid>

        <Box className={styles.modalActions}>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={
              !formData.storeNumber ||
              !formData.storeName ||
              !formData.streetAddress ||
              !formData.city ||
              !formData.state ||
              !formData.country ||
              !formData.phone
            }
          >
            Create Store
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default NewStoreModal;
