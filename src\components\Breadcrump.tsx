import React from "react";
import { Breadcrumbs, <PERSON> } from "@mars/vizx-react";
import styles from "../styles/Breadcrump.module.css";

interface BreadcrumbProps {
  items: string[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items }) => {
  return (
    <Breadcrumbs separator=">">
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        return (
          <Link
            key={index}
            underline="hover"
            color="inherit"
            href="#"
            className={`${styles.link} ${isLast ? styles.last : ""}`}
          >
            {item}
          </Link>
        );
      })}
    </Breadcrumbs>
  );
};

export default Breadcrumb;
