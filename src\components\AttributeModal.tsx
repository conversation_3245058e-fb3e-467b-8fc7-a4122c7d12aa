import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  Grid,
  Box,
  Divider,
  FormHelperText,
} from "@mui/material";

interface AttributeModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (attribute: any) => void;
  mode: "add" | "edit";
  initialData?: any;
}

const style = {
  position: "absolute" as const,
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 800,
  maxWidth: "95%",
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: "24px 16px",
  maxHeight: "90vh",
  overflowY: "auto",
};

const AttributeModal: React.FC<AttributeModalProps> = ({
  open,
  onClose,
  onSave,
  mode,
  initialData,
}) => {
  const [formData, setFormData] = useState({
    ListName: "",
    ItemName: "",
    ItemValue: "",
    ItemOrder: "",
    ItemDescription: "",
    ParentListName: "",
    ParentItemValue: "",
  });
  
  const [errors, setErrors] = useState({
    ListName: false,
    ItemName: false,
    ItemValue: false,
    ItemOrder: false,
  });

  useEffect(() => {
    if (mode === "edit" && initialData) {
      setFormData({
        ListName: initialData.ListName || "",
        ItemName: initialData.ItemName || "",
        ItemValue: initialData.ItemValue || "",
        ItemOrder: initialData.ItemOrder?.toString() || "",
        ItemDescription: initialData.ItemDescription || "",
        ParentListName: initialData.ParentListName || "",
        ParentItemValue: initialData.ParentItemValue || "",
      });
    } else {
      setFormData({
        ListName: "",
        ItemName: "",
        ItemValue: "",
        ItemOrder: "",
        ItemDescription: "",
        ParentListName: "",
        ParentItemValue: "",
      });
    }
    // Reset errors when modal opens
    setErrors({
      ListName: false,
      ItemName: false,
      ItemValue: false,
      ItemOrder: false,
    });
  }, [mode, initialData, open]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when user starts typing
    if (name in errors) {
      setErrors(prev => ({
        ...prev,
        [name]: false
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {
      ListName: !formData.ListName.trim(),
      ItemName: !formData.ItemName.trim(),
      ItemValue: !formData.ItemValue.trim(),
      ItemOrder: !formData.ItemOrder.trim(),
    };
    
    setErrors(newErrors);
    return !newErrors.ListName && !newErrors.ItemName && 
           !newErrors.ItemValue && !newErrors.ItemOrder;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }
    
    const attributeData = {
      ListName: formData.ListName,
      ItemName: formData.ItemName,
      ItemValue: formData.ItemValue,
      ItemOrder: parseInt(formData.ItemOrder),
      ItemDescription: formData.ItemDescription || undefined,
      ParentListName: formData.ParentListName || undefined,
      ParentItemValue: formData.ParentItemValue || undefined,
    };
    onSave(attributeData);
  };

  const renderField = (
    label: string,
    name: keyof typeof formData,
    required = false,
    multiline = false,
    rows = 1,
    type = "text"
  ) => (
    <Grid container alignItems="center" spacing={2} sx={{ mb: 2 }}>
      <Grid item xs={4} sx={{ textAlign: "right", pr: 2 }}>
        <Typography sx={{ fontSize: 12, fontWeight: 600 }}>
          {label}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
      </Grid>
      <Grid item xs={8}>
        <TextField
          name={name}
          value={formData[name]}
          onChange={handleChange}
          variant="outlined"
          fullWidth
          size="small"
          multiline={multiline}
          rows={rows}
          type={type}
          error={errors[name as keyof typeof errors]}
          sx={{
            "& .MuiInputBase-input": {
              fontSize: 14,
              padding: "10px",
            },
          }}
        />
        {errors[name as keyof typeof errors] && (
          <FormHelperText error sx={{ mt: 0.5 }}>
            This field is required
          </FormHelperText>
        )}
      </Grid>
    </Grid>
  );

  return (
    <Modal open={open} onClose={onClose}>
      <Box sx={style}>
        <Typography
          variant="h6"
          sx={{
            fontFamily: '"Mars Centra", sans-serif',
            fontWeight: 700,
            fontSize: "12px",
            lineHeight: "100%",
            letterSpacing: "0%",
            textTransform: "uppercase",
            mb: 3,
            px: 1,
          }}
        >
          {mode === "add" ? "Add Attribute" : "Edit Attribute"}
        </Typography>

        <Box sx={{ px: 1, mb: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={6}>
              {renderField("List Name", "ListName", true)}
              {renderField("Item Name", "ItemName", true)}
              {renderField("Item Value", "ItemValue", true)}
              {renderField("Item Order", "ItemOrder", true, false, 1, "number")}
            </Grid>
            <Grid item xs={6}>
              {renderField("Parent List Name", "ParentListName")}
              {renderField("Parent Item Value", "ParentItemValue")}
              {renderField("Item Description", "ItemDescription", false, true, 3)}
            </Grid>
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-start",
            gap: 2,
            px: 1,
            pt: 1,
          }}
        >
          <Button
            variant="contained"
            onClick={handleSubmit}
            sx={{
              backgroundColor: "#00008B",
              color: "#fff",
              fontWeight: 600,
              px: 4,
              borderRadius: 1,
              "&:hover": { backgroundColor: "#000070" },
            }}
          >
            Save
          </Button>
          <Button
            variant="outlined"
            onClick={onClose}
            sx={{
              color: "#00008B",
              borderColor: "#00008B",
              fontWeight: 600,
              px: 4,
              borderRadius: 1,
              "&:hover": {
                backgroundColor: "#f0f0f0",
                borderColor: "#00008B",
              },
            }}
          >
            Cancel
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default AttributeModal;