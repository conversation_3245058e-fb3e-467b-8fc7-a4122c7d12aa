const express = require("express");
const cors = require("cors");
const app = express();

app.use(cors());
app.use(express.json());

// In-memory storage
let todos = [
  { id: 1, text: "<PERSON> Prettier", completed: false },
  { id: 2, text: "<PERSON> Linter", completed: false },
  { id: 3, text: "Check Unit Tests", completed: false },
];
let nextId = 4;

// Get all todos
app.get("/todos", (req, res) => {
  res.json(todos);
});

// Create new todo
app.post("/todos", (req, res) => {
  const { text } = req.body;
  const newTodo = {
    id: nextId++,
    text,
    completed: false,
  };
  todos.push(newTodo);
  res.status(201).json(newTodo);
});

// Toggle todo completion
app.patch("/todos/:id", (req, res) => {
  const id = parseInt(req.params.id);
  const todo = todos.find((t) => t.id === id);

  if (!todo) {
    return res.status(404).json({ error: "Todo not found" });
  }

  todo.completed = !todo.completed;

  res.json(todo);
});
// Delete todo
app.delete("/todos/:id", (req, res) => {
  const id = parseInt(req.params.id);
  const todoIndex = todos.findIndex((t) => t.id === id);

  if (todoIndex === -1) {
    return res.status(404).json({ error: "Todo not found" });
  }

  todos.splice(todoIndex, 1);
  res.status(204).send();
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
