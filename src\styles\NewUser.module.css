/* NewUser.module.css */
.title {
    font-family: 'Mars Centra';
    font-weight: 700;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    margin-bottom: 16px;
  }
  
  .divider {
    border: 1px solid rgba(216, 216, 216, 1);
    margin-bottom: 24px;
  }
  
  .label {
    font-family: 'Mars Centra';
    font-weight: 400;
    font-size: 12px;
    line-height: 100%;
    letter-spacing: 0%;
    color: rgba(60, 60, 112, 1);
    display: block;
    margin-bottom: 8px;
  }
  
  .formRow {
    margin-bottom: 20px;
  }
  
  .checkboxContainer {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .checkboxLabel {
    font-family: 'Mars Centra';
    font-size: 14px;
    margin-left: 8px;
  }
  
  .buttonContainer {
    display: flex;
    gap: 16px;
    margin-top: 32px;
  }
  
  .saveButton {
    background-color: #00008B !important;
    color: white !important;
    font-weight: 600 !important;
    padding: '8px 24px' !important;
  }
  
  .cancelButton {
    color: #00008B !important;
    border-color: #00008B !important;
    font-weight: 600 !important;
  }
  
  @media (max-width: 768px) {
    .formRow {
      flex-direction: column;
    }
    
    .label {
      margin-bottom: 4px;
    }
  }