import { createAsyncThunk } from "@reduxjs/toolkit";
import StoreService from "../api/store.service";
import {
  ChangeLogParams,
  DeleteRequest,
  DistStoreParams,
  FindStoreParams,
  MappingDetailsParams,
  MappingUnmappingMetadataRequest,
  MappingUnmappingUpdateMetadataRequest,
  NutroDetailsStoreParams,
  StoreCreateRequest,
  StoreParams,
  StoreSuggestionParams,
  StoreUpdateRequest,
  UnmappedStoreParams,
} from "../../types/store.types";

export const getSearchStores = createAsyncThunk(
  "store/getSearchStores",
  async (params: StoreParams) => {
    const response = await StoreService.getSearchStores(params);
    return response;
  },
);
export const getNutroStoreDetail = createAsyncThunk(
  "store/getNutroStoreDetail",
  async (params: NutroDetailsStoreParams) => {
    const response = await StoreService.getNutroStoreDetail(params);
    return response;
  },
);

export const getNutroStoreMetadata = createAsyncThunk(
  "store/getNutroStoreMetadata",
  async (nutrostore_id: number) => {
    const response = await StoreService.getNutroStoreMetadata(nutrostore_id);
    return response;
  },
);

export const searchDistStores = createAsyncThunk(
  "store/searchDistStores",
  async (params: DistStoreParams) => {
    const response = await StoreService.searchDistStores(params);
    return response;
  },
);

export const searchUnmappedStores = createAsyncThunk(
  "store/searchUnmappedStores",
  async (params: UnmappedStoreParams) => {
    const response = await StoreService.searchUnmappedStores(params);
    return response;
  },
);

export const getChangeLog = createAsyncThunk(
  "store/getChangeLog",
  async (params: ChangeLogParams) => {
    const response = await StoreService.getChangeLog(params);
    return response;
  },
);
export const getStoreSuggestions = createAsyncThunk(
  "store/getStoreSuggestions",
  async (params: StoreSuggestionParams) => {
    const response = await StoreService.getStoreSuggestions(params);
    return response;
  },
);
export const createStore = createAsyncThunk(
  "store/createStore",
  async (storeData: StoreCreateRequest, { rejectWithValue }) => {
    try {
      const response = await StoreService.createStore(storeData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to create store");
    }
  },
);

export const getMappingDetails = createAsyncThunk(
  "store/getMappingDetails",
  async (params: MappingDetailsParams) => {
    const response = await StoreService.getMappingDetails(params);
    console.log("response ", response);
    return response;
  },
);
export const updateStoreMapping = createAsyncThunk(
  "store/updateStoreMapping",
  async (requestData: MappingUnmappingMetadataRequest, { rejectWithValue }) => {
    try {
      const response = await StoreService.updateStoreMapping(requestData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to update store mapping");
    }
  },
);

export const updateStoreMappingMetadata = createAsyncThunk(
  "store/updateStoreMappingMetadata",
  async (requestData: MappingUnmappingUpdateMetadataRequest, { rejectWithValue }) => {
    try {
      const response = await StoreService.updateStoreMappingMetadata(requestData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to update metadata");
    }
  },
);
export const getAllStores = createAsyncThunk(
  "store/getAllStores",
  async (
    params: {
      page?: number;
      limit?: number;
      search_query?: string;
      store_ids?: number[];
      store_names?: string[];
      chain_names?: string[];
      territory_managers?: string[];
      district_managers?: string[];
      store_numbers?: string[];
      account_types?: string[];
      report_categories?: string[];
    },
    { rejectWithValue },
  ) => {
    try {
      // Convert arrays to comma-separated strings
      const apiParams = {
        ...params,
        store_ids: params.store_ids?.join(","),
        store_names: params.store_names?.join(","),
        chain_names: params.chain_names?.join(","),
        territory_managers: params.territory_managers?.join(","),
        district_managers: params.district_managers?.join(","),
        store_numbers: params.store_numbers?.join(","),
        account_types: params.account_types?.join(","),
        report_categories: params.report_categories?.join(","),
        search_query: params.search_query,
      };

      const response = await StoreService.getAllStores(apiParams);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to fetch stores");
    }
  },
);

export const getAllStoresTotal = createAsyncThunk(
  "store/getAllStoresTotal",
  async (
    params: {
      search_query?: string;
      store_ids?: number[];
      store_names?: string[];
      chain_names?: string[];
      territory_managers?: string[];
      district_managers?: string[];
      store_numbers?: string[];
      account_types?: string[];
      report_categories?: string[];
      count_only?: boolean;
      limit?: number;
      offset?: number;
    },
    { rejectWithValue },
  ) => {
    try {
      // Convert arrays to comma-separated strings
      const apiParams = {
        ...params,
        store_ids: params.store_ids?.join(","),
        store_names: params.store_names?.join(","),
        chain_names: params.chain_names?.join(","),
        territory_managers: params.territory_managers?.join(","),
        district_managers: params.district_managers?.join(","),
        store_numbers: params.store_numbers?.join(","),
        account_types: params.account_types?.join(","),
        report_categories: params.report_categories?.join(","),
        search_query: params.search_query,
      };

      const response = await StoreService.getAllStoresTotal(apiParams);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to fetch stores");
    }
  },
);

// Add new thunk for export progress tracking
export const trackExportProgress = createAsyncThunk(
  "store/trackExportProgress",
  async (progress: number) => {
    return progress;
  },
);

export const getStoreFilterValues = createAsyncThunk(
  "store/getStoreFilterValues",
  async (
    {
      field,
      search,
      limit,
      offset,
    }: { field: string; search?: string; limit?: number; offset?: number },
    { rejectWithValue },
  ) => {
    try {
      const response = await StoreService.getStoreFilterValues(field, search, limit, offset);
      return {
        field,
        values: Array.isArray(response.values) ? response.values : [],
        total: typeof response.total === "number" ? response.total : 0,
      };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to fetch filter values");
    }
  },
);
export const updateStore = createAsyncThunk(
  "store/updateStore",
  async (
    {
      nutro_store_id,
      updateData,
    }: {
      nutro_store_id: number;
      updateData: StoreUpdateRequest;
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await StoreService.updateStore(nutro_store_id, updateData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to update store");
    }
  },
);

export const deleteStores = createAsyncThunk(
  "store/deleteStores",
  async (deleteRequest: DeleteRequest, { rejectWithValue }) => {
    try {
      const response = await StoreService.deleteStores(deleteRequest);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to delete stores");
    }
  },
);

export const findStores = createAsyncThunk(
  "store/findStores",
  async (params: FindStoreParams, { rejectWithValue }) => {
    try {
      const response = await StoreService.findStores(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Failed to search stores");
    }
  },
);
