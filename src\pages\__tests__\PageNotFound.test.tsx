import { render, screen } from "@testing-library/react";
import PageNotFound from "../PageNotFound";
import { vi } from "vitest";
import { act } from "react";

// Update the initial mock setup
const mockNavigate = vi.fn();

vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe("PageNotFound", () => {
  it("should render PageErrorFallback with error message", () => {
    render(<PageNotFound />);
    expect(screen.getByText("Something went wrong")).toBeInTheDocument();
    expect(screen.getByText("Page not found")).toBeInTheDocument();
  });

  it("should navigate to the root when reset button is clicked", () => {
    render(<PageNotFound />);
    act(() => {
      screen.getByText("Goto home page").click();
    });
    expect(mockNavigate).toHaveBeenCalledWith("");
  });
});
