import { Configuration, LogLevel } from "@azure/msal-browser";
import msalConstants from "./constants";

const development: boolean = process.env.NODE_ENV === "development";

const msalConfig: Configuration = {
  auth: {
    clientId: msalConstants.clientId,
    authority: msalConstants.authority,
    redirectUri: `${window.location.origin}`,
    postLogoutRedirectUri: `${window.location.origin}`,
  },
  cache: {
    cacheLocation: "sessionStorage",
    storeAuthStateInCookie: true,
  },
  system: {
    allowRedirectInIframe: true,
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (!development) {
          return;
        }
        if (containsPii) {
          return;
        }
        switch (level) {
          case LogLevel.Error:
            console.error(message);
            return;
          case LogLevel.Info:
            console.info(message);
            return;
          case LogLevel.Verbose:
            console.debug(message);
            return;
          case LogLevel.Warning:
            console.warn(message);
            return;
          default:
            console.log(message);
        }
      },
    },
  },
};

const graphRequestScopes = { ...msalConstants.graphRequestScopes };
const apiRequestScopes = { ...msalConstants.apiRequestScopes };

export { apiRequestScopes, graphRequestScopes, msalConfig };
