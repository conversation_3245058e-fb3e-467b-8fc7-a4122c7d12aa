import React, { useState, useEffect, useCallback } from "react";
import TopBarLayout from "../../components/TopBarLayout";
import TableComponent from "../../components/TableComponent";
import { Box, debounce } from "@mui/material";
import FilterRow from "../../components/FilterRow";
import { useNavigate } from "react-router-dom";
import { setSearchQuery, setStoreFilters, resetStoreFilters } from "../../store/slice/storeSlice";
import { searchDistStores } from "../../services/action/store.action";
import { ThunkDispatch } from "redux-thunk";
import { UnknownAction } from "@reduxjs/toolkit";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { DistStoreItem } from "../../types/store.types";
import { toast } from "react-toastify";

type AppDispatch = ThunkDispatch<RootState, unknown, UnknownAction>;

const MappedStoreSearch = () => {
  const dispatch: AppDispatch = useDispatch();
  const navigate = useNavigate();
  const { distStores, isLoading, filters, generic_search, distStoresTotalCount } = useSelector(
    (state: RootState) => state.store,
  );

  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });

  // Show toast when no results are found
  useEffect(() => {
    if (
      !isLoading &&
      distStores.length === 0 &&
      (generic_search || Object.values(filters).some(Boolean))
    ) {
    }
  }, [distStores, isLoading, generic_search, filters]);

  // Initial load
  useEffect(() => {
    dispatch(
      searchDistStores({
        limit: pagination.rowsPerPage,
        page: pagination.page,
      }),
    );
  }, [dispatch, pagination.page, pagination.rowsPerPage]);

  // Reset filters when component mounts
  useEffect(() => {
    dispatch(resetStoreFilters());
    dispatch(setSearchQuery(""));
  }, [dispatch]);

  const fetchStores = useCallback(() => {
    dispatch(
      searchDistStores({
        limit: pagination.rowsPerPage,
        page: pagination.page,
        generic_search,
        ...filters,
      }),
    );
  }, [dispatch, filters, generic_search, pagination.page, pagination.rowsPerPage]);

  useEffect(() => {
    fetchStores();
  }, [pagination.page, pagination.rowsPerPage]);

  // Immediate API call on generic search change
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (generic_search !== undefined) {
        fetchStores();
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [generic_search]);

  const handleSearch = useCallback(
    debounce((query: string) => {
      dispatch(setSearchQuery(query));
      setPagination((prev) => ({ ...prev, page: 1 }));
    }, 500),
    [dispatch],
  );

  const handleFilterChange = (name: string, value: string) => {
    dispatch(setStoreFilters({ [name]: value }));
  };

  const handleApplyFilters = () => {
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchStores();
  };

  const handleResetFilters = () => {
    dispatch(resetStoreFilters());
    dispatch(setSearchQuery(""));
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchStores();
  };

  const handleNutroIDClick = (nutroStoreID: string) => {
    navigate(`/store-search/${nutroStoreID}`);
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const handleStoreDescClick = (row: DistStoreItem) => {
    if (!row?.VCID) {
      console.error("StoreID is missing in row data");
      return;
    }
    navigate(`/store-desc/${row.VCID}`, { state: { distStoreId: row.StoreID } });
  };

  const columns = [
    {
      id: "DataSource",
      label: "Data Source Name",
      description: "Name of the external data source",
    },
    {
      id: "StoreID",
      label: "Store ID",
      description: "ID in the external system",
    },
    {
      id: "StoreDesc",
      label: "Store Desc",
      description: "Description of the store",
      format: (value: string, row: DistStoreItem) => {
        if (!row) {
          console.error("Row is undefined in StoreDesc format");
          return <span>{value}</span>;
        }
        return (
          <Box
            component="span"
            sx={{
              color: "primary.main",
              textDecoration: "underline",
              cursor: "pointer",
              "&:hover": {
                color: "primary.dark",
                fontWeight: "500",
              },
            }}
            onClick={() => handleStoreDescClick(row)}
          >
            {value}
          </Box>
        );
      },
    },
    {
      id: "CityStateZip",
      label: "City/State/ZIP",
      description: "Location information",
    },
    {
      id: "FirstDt",
      label: "First DT",
      description: "First data transfer date",
    },
    {
      id: "LastDt",
      label: "Last DT",
      description: "Last data transfer date",
    },
    {
      id: "MappedTo",
      label: "Mapped To",
      description: "Nutro ID this store is mapped to",
      format: (value: string) => {
        if (!value) {
          return <span>-</span>;
        }
        return (
          <Box
            component="span"
            sx={{
              color: "primary.main",
              textDecoration: "underline",
              cursor: "pointer",
              "&:hover": {
                color: "primary.dark",
                fontWeight: "500",
              },
            }}
            onClick={() => handleNutroIDClick(value)}
          >
            {value}
          </Box>
        );
      },
    },
    {
      id: "IsPending",
      label: "Status",
      description: "Current status of the mapping",
      format: (value: boolean) => (
        <Box
          sx={{
            color: value ? "success.main" : "error.main",
            fontWeight: "bold",
          }}
        >
          {value ? "True" : "False"}
        </Box>
      ),
    },
  ];

  const filterConfigs = [
    {
      name: "DataSource",
      label: "Data Source",
      type: "text" as const,
      value: filters.DataSource || "",
      onChange: (value: string) => handleFilterChange("DataSource", value),
    },
    {
      name: "DistStoreID",
      label: "Store ID",
      type: "text" as const,
      value: filters.DistStoreID || "",
      onChange: (value: string) => handleFilterChange("DistStoreID", value),
    },
    {
      name: "DistStoreName",
      label: "Store Name",
      type: "text" as const,
      value: filters.DistStoreName || "",
      onChange: (value: string) => handleFilterChange("DistStoreName", value),
    },
    {
      name: "DistStoreAddress",
      label: "Store Address",
      type: "text" as const,
      value: filters.DistStoreAddress || "",
      onChange: (value: string) => handleFilterChange("DistStoreAddress", value),
    },
    {
      name: "DistStoreCity",
      label: "Store City",
      type: "text" as const,
      value: filters.DistStoreCity || "",
      onChange: (value: string) => handleFilterChange("DistStoreCity", value),
    },
    {
      name: "DistStoreState",
      label: "Store State",
      type: "text" as const,
      value: filters.DistStoreState || "",
      onChange: (value: string) => handleFilterChange("DistStoreState", value),
    },
    {
      name: "DistStoreZip",
      label: "Store ZIP",
      type: "text" as const,
      value: filters.DistStoreZip || "",
      onChange: (value: string) => handleFilterChange("DistStoreZip", value),
    },
    {
      name: "DistStorePhone",
      label: "Store Phone",
      type: "text" as const,
      value: filters.DistStorePhone || "",
      onChange: (value: string) => handleFilterChange("DistStorePhone", value),
    },
  ];

  return (
    <div>
      <TopBarLayout breadcrumbItems={["Admin", "Mapped Search"]} onSearchChange={handleSearch} />

      <FilterRow
        filters={filterConfigs}
        onReset={handleResetFilters}
        onApply={handleApplyFilters}
      />

      {!isLoading && distStores.length === 0 ? (
        <Box sx={{ p: 3, textAlign: "center" }}>
          No mapped stores found matching your search criteria
        </Box>
      ) : (
        <TableComponent
          columns={columns}
          rows={distStores}
          rowsPerPage={pagination.rowsPerPage}
          page={pagination.page}
          totalCount={distStoresTotalCount}
          onPageChange={handlePageChange}
          showActions={false}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default MappedStoreSearch;
