import { Error<PERSON><PERSON><PERSON>, PageErrorFallback } from "@mars/vizx-react";
import { ErrorBoundary, FallbackProps } from "./ErrorBoundary";

export const WithErrorBoundary = (
  Component: React.ComponentType,
  FallbackComponent?: React.ComponentType<FallbackProps>,
) => {
  const handleReset = () => {
    //reset state etc.
  };

  return (
    <ErrorBoundary FallbackComponent={FallbackComponent ?? ErrorFallback} onReset={handleReset}>
      <Component />
    </ErrorBoundary>
  );
};

export const WithPageErrorBoundary = (Component: React.ComponentType) =>
  WithErrorBoundary(Component, PageErrorFallback);
