import React, { useEffect, useState } from "react";
import { Header } from "@mars/vizx-react";
import { useNavigate, useLocation } from "react-router-dom";
import logoImage from "../assets/mars-petcare.png";
import MenuBar from "./MenuBar";
import styles from "../styles/AppHeader.module.css";

type HeaderMenuItem = {
  id: string;
  title: string;
};

export const AppHeader: React.FC = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [selectedMenuItem, setSelectedMenuItem] = useState<string>(pathname);

  const userMenuItems = [
    { id: "/myProfile", title: "My Profile" },
    { id: "/history", title: "History" },
    { id: "/logoff", title: "Logoff" },
  ];

  const handleLogoClick = () => {
    setSelectedMenuItem("/");
    navigate("/");
  };

  const handleUserMenuItem = (item: HeaderMenuItem) => {
    if (item.id) navigate(item.id);
  };

  useEffect(() => {
    setSelectedMenuItem(pathname);
  }, [pathname]);

  const logo = <img className="h-[40px]" src={logoImage} alt="logo" />;

  return (
    <div className={styles["header-container"]}>
      {/* Top Header */}
      <Header
        logo={logo}
        title="MPDDM"
        onLogoClick={handleLogoClick}
        showUserIcon={true}
        userMenuItems={userMenuItems}
        onUserMenuItem={handleUserMenuItem}
        showNotificationsButton={true}
        userDisplayName="John Doe"
      />

      {/* Menu Bar below header */}
      <div className={styles["menu-bar-container"]}>
        <MenuBar selectedMenuItem={selectedMenuItem} />
      </div>
    </div>
  );
};
