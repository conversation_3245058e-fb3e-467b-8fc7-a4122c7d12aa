import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import TableComponent from "../TableComponent"; 

const columns = [
  { id: "name", label: "Name" },
  { id: "email", label: "Email" },
];

const rows = [
  { name: "<PERSON>", email: "<EMAIL>" },
  { name: "<PERSON>", email: "<EMAIL>" },
  { name: "<PERSON>", email: "<EMAIL>" },
  { name: "<PERSON>", email: "<EMAIL>" },
  { name: "<PERSON>", email: "<EMAIL>" },
  { name: "<PERSON>", email: "<EMAIL>" },
];

describe("TableComponent", () => {
  let onEditMock: any;
  let onDeleteMock: any;

  beforeEach(() => {
    onEditMock = vi.fn();
    onDeleteMock = vi.fn();
  });

  it("renders column headers", () => {
    render(<TableComponent columns={columns} rows={rows} onEdit={onEditMock} onDelete={onDeleteMock} />);

    columns.forEach((col) => {
      expect(screen.getByText(col.label)).toBeInTheDocument();
    });

    expect(screen.getByText(/Action/i)).toBeInTheDocument();
  });

  it("renders only the specified number of rows per page", () => {
    render(
      <TableComponent
        columns={columns}
        rows={rows}
        onEdit={onEditMock}
        onDelete={onDeleteMock}
        rowsPerPage={3}
      />
    );

    const tableRows = screen.getAllByRole("row");
    // +1 for header row
    expect(tableRows.length).toBe(4);
  });

  it("calls onEdit when edit button is clicked", () => {
    render(<TableComponent columns={columns} rows={rows} onEdit={onEditMock} onDelete={onDeleteMock} />);

    const editButtons = screen.getAllByRole("button", { name: /edit/i });
    fireEvent.click(editButtons[0]);

    expect(onEditMock).toHaveBeenCalledWith(rows[0]);
  });

  it("calls onDelete when delete button is clicked", () => {
    render(<TableComponent columns={columns} rows={rows} onEdit={onEditMock} onDelete={onDeleteMock} />);

    const deleteButtons = screen.getAllByRole("button", { name: /delete/i });
    fireEvent.click(deleteButtons[0]);

    expect(onDeleteMock).toHaveBeenCalledWith(rows[0]);
  });

  it("navigates to next page on 'Next' click", () => {
    render(
      <TableComponent
        columns={columns}
        rows={rows}
        onEdit={onEditMock}
        onDelete={onDeleteMock}
        rowsPerPage={2}
      />
    );

    fireEvent.click(screen.getByText(/Next/i));
    expect(screen.getByText("Charlie")).toBeInTheDocument();
  });

  it("navigates to previous page on 'Back' click", () => {
    render(
      <TableComponent
        columns={columns}
        rows={rows}
        onEdit={onEditMock}
        onDelete={onDeleteMock}
        rowsPerPage={2}
      />
    );

    fireEvent.click(screen.getByText(/Next/i)); // Page 2
    fireEvent.click(screen.getByText(/Back/i)); // Back to Page 1

    expect(screen.getByText("Alice")).toBeInTheDocument();
  });

  it("disables 'Back' button on first page", () => {
    render(<TableComponent columns={columns} rows={rows} onEdit={onEditMock} onDelete={onDeleteMock} />);
    expect(screen.getByText(/Back/i)).toBeDisabled();
  });

  it("disables 'Next' button on last page", () => {
    render(
      <TableComponent
        columns={columns}
        rows={rows}
        onEdit={onEditMock}
        onDelete={onDeleteMock}
        rowsPerPage={6}
      />
    );

    expect(screen.getByText(/Next/i)).toBeDisabled();
  });

  it("clicks a specific pagination number", () => {
    render(
      <TableComponent
        columns={columns}
        rows={rows}
        onEdit={onEditMock}
        onDelete={onDeleteMock}
        rowsPerPage={2}
      />
    );

    fireEvent.click(screen.getByText("3")); // Go to page 3
    expect(screen.getByText("Eve")).toBeInTheDocument();
  });
});
