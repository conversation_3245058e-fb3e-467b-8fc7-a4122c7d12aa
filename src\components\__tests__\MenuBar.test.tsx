import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import MenuBar from '../MenuBar';
import { useNavigate } from 'react-router-dom';
import MenuItemWithDropdown from '../MenuItemWithDropdown';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('../MenuItemWithDropdown', () => ({
  default: vi.fn(({ label }) => <div data-testid="menu-dropdown">{label}</div>),
}));

vi.mock('@mars/vizx-react', () => ({
  Button: vi.fn(({ children, onClick }) => (
    <button onClick={onClick} data-testid="menu-button">
      {children}
    </button>
  )),
}));

describe('MenuBar Component', () => {
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
  });

  it('renders home button and dropdown menus', () => {
    render(<MenuBar selectedMenuItem="/" />);
    expect(screen.getByTestId('menu-button')).toHaveTextContent('Home');
    expect(screen.getAllByTestId('menu-dropdown')).toHaveLength(3);
  });

  it('navigates when home button is clicked', () => {
    render(<MenuBar selectedMenuItem="/" />);
    fireEvent.click(screen.getByTestId('menu-button'));
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  // it('applies active style to selected menu item', () => {
  //   const { rerender } = render(<MenuBar selectedMenuItem="/" />);
  //   expect(screen.getByTestId('menu-button')).toHaveStyle('border-bottom: 2px solid white');
    
  //   rerender(<MenuBar selectedMenuItem="/other" />);
  //   expect(screen.getByTestId('menu-button')).toHaveStyle('border-bottom: none');
  // });

  it('has correct background color', () => {
    const { container } = render(<MenuBar selectedMenuItem="/" />);
    expect(container.firstChild).toHaveStyle('background-color: rgba(255, 20, 20, 1)');
  });
});