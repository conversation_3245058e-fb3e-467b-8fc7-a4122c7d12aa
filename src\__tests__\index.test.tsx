import { describe, it, expect, vi } from "vitest";
import React from "react";
import ReactDOM from "react-dom/client";
import { App } from "../App.tsx";

// Mock React DOM
vi.mock("react-dom/client", () => ({
  createRoot: vi.fn(() => ({
    render: vi.fn(),
  })),
  default: {
    createRoot: vi.fn(() => ({
      render: vi.fn(),
    })),
  },
}));

// Mock App component
vi.mock("../App", () => ({
  App: () => <div data-testid="mock-app">Mock App</div>,
}));

describe("Index", () => {
  // Cleanup after test
  afterEach(() => {
    document.body.innerHTML = "";
    vi.clearAllMocks();
  });

  it("renders App component inside StrictMode", async () => {
    // Mock getElementById
    const root = document.createElement("div");
    root.id = "root";
    document.body.appendChild(root);

    // Import index.tsx using dynamic import
    await import("../index.tsx");

    // Verify createRoot was called with the root element
    expect(ReactDOM.createRoot).toHaveBeenCalledWith(root);

    // Verify render was called with StrictMode wrapping App
    const createRootMock = ReactDOM.createRoot as unknown as ReturnType<typeof vi.fn>;
    const renderMock = createRootMock.mock.results[0].value.render;

    expect(renderMock).toHaveBeenCalledTimes(1);
    const renderArg = renderMock.mock.calls[0][0];

    // Check that StrictMode is the outer component
    expect(renderArg.type).toBe(React.StrictMode);
  });
});
