import React from "react";
import { ErrorBoundary } from "./ErrorBoundary";
import { GlobalErrorFallback } from "./GlobalErrorFallback";
import PropTypes from "prop-types";

export type GlobalErrorBoundary = {
  children?: React.ReactNode;
};

export const GlobalErrorBoundary: React.FC<GlobalErrorBoundary> = ({ children }) => {
  return <ErrorBoundary FallbackComponent={GlobalErrorFallback}>{children}</ErrorBoundary>;
};

GlobalErrorBoundary.propTypes = {
  children: PropTypes.element,
};

GlobalErrorBoundary.displayName = "GlobalErrorBoundary";
