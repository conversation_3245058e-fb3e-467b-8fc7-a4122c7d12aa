import React from "react";
import logoImage from "../assets/mars-petcare.png"; 
import storeImage from "../assets/store.png"; 
import productImage from "../assets/product.png"; 
import adminImage from "../assets/admin.png"; 

import CapabilityCard from "../components/Card";
import styles from '../styles/HomeComponent.module.css';

const Home: React.FC = () => {
  return (
    <>
      <div className={styles["home-container"]}>
        <div className={styles.header}>
          {/* Left Section */}
          <div className={styles["left-section"]}>
            <h1 className={styles.heading}>MPDDM</h1>
            <p className={styles.description}>
              Mars Pet Distributor Data Management current approach allows the admin to track and monitor the distributor data, 
              the business to edit to the blob store and navigate within the filesystem, which poses a security risk, 
              especially in the production environment. MPDDM allows users to map and attribute data for US and Canada 
              Mars Pet distributor data sources.
            </p>
          </div>

          {/* Right Section (Logo) */}
          <div className={styles["right-section"]}>
            <img src={logoImage} alt="Mars Logo" className={styles.logo} />
          </div>
        </div>
      </div>

      {/* App Capabilities Heading */}
      <h2 className={styles["app-capabilities"]}>App Capabilities</h2>

      {/* Capability Cards */}
      <div className={styles["capabilities-container"]}>
        <CapabilityCard title="Store" logo={storeImage} route="/store-search" />
        <CapabilityCard title="Product" logo={productImage} route="/product" />
        <CapabilityCard title="Admin" logo={adminImage} route="/attribute-list" />
      </div>
    </>
  );
};

export default Home;
