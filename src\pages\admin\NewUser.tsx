import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  TextField,
  Grid,
  Typography,
  Select,
  MenuItem,
  FormControl,
  Checkbox,
  FormControlLabel,
  Divider,
  InputLabel,
} from "@mui/material";
import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import TopBarLayout from "../../components/TopBarLayout";
import styles from "../../styles/NewUser.module.css";
import { RootState } from "../../store";
import {
  assignUserRole,
  createUser,
  getAllManagers,
  getAllRoles,
} from "../../services/action/user.action";
import { useAppDispatch, useAppSelector } from "../../services/hooks/store.hooks";

const userTypes = [
  { value: "Customer", label: "Customer" },
  { value: "NPDDC", label: "NPDDC" },
  { value: "Partner", label: "Partner" },
];

const NewUser = () => {
  const appDispatch = useAppDispatch();
  const navigate = useNavigate();

  const { roles, managers } = useAppSelector((state: RootState) => state.admin);

  // Fetch roles and managers on component mount
  useEffect(() => {
    appDispatch(getAllRoles());
    appDispatch(getAllManagers());
  }, [appDispatch]);

  const [formData, setFormData] = useState({
    UserLogin: "",
    UserFirstName: "",
    UserLastName: "",
    UserFullName: "",
    UserEmail: "",
    MarsUsername: "",
    UserType: "",
    ManagerID: "",
    Active: true,
    RoleID: "",
  });

  const [errors, setErrors] = useState({
    UserLogin: false,
    UserFirstName: false,
    UserLastName: false,
    UserEmail: false,
    UserType: false,
    RoleID: false,
    MarsUsername: false,
  });

  const validateForm = () => {
    const newErrors = {
      UserLogin: !formData.UserLogin,
      UserFirstName: !formData.UserFirstName,
      UserLastName: !formData.UserLastName,
      UserEmail: !formData.UserEmail,
      UserType: !formData.UserType,
      RoleID: !formData.RoleID,
      MarsUsername: formData.MarsUsername.length > 8,
    };
    setErrors(newErrors);
    return !Object.values(newErrors).some(Boolean);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: false }));
    }
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user selects an option
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: false }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      if (formData.MarsUsername.length > 8) {
        toast.error("Mars Username must be 8 characters or less");
      } else {
        toast.error("Please fill in all required fields");
      }
      return;
    }

    try {
      // Prepare user data
      const userData = {
        ...formData,
        UserFullName: `${formData.UserFirstName} ${formData.UserLastName}`.trim(),
        ManagerID: formData.ManagerID || null,
        IsLockedOut: false,
      };

      // First create the user
      const createUserResponse = await appDispatch(createUser(userData)).unwrap();

      // If user was created successfully and we have a RoleID selected
      if (createUserResponse.UserID && formData.RoleID) {
        // Then assign the role
        await appDispatch(
          assignUserRole({
            userId: createUserResponse.UserID,
            roleId: formData.RoleID,
          }),
        ).unwrap();
      }

      // Navigate to user list on success
      navigate("/user-list");
    } catch (error: any) {
      if (error.message && error.message.includes("already exists")) {
        toast.error(error.message);
      } else {
        toast.error("Failed to create user. Please try again.");
      }
      console.error("Error creating user or assigning role:", error);
    }
  };

  const renderTextField = (
    name: keyof typeof formData,
    label: string,
    type = "text",
    required = false,
  ) => (
    <Box className={styles.formRow}>
      <InputLabel htmlFor={name} className={styles.label} data-testid={`${name}-label`}>
        {label} {required && <span style={{ color: "red" }}>*</span>}
      </InputLabel>
      <TextField
        id={name}
        name={name}
        value={formData[name]}
        onChange={handleChange}
        variant="outlined"
        fullWidth
        size="small"
        type={type}
        error={errors[name as keyof typeof errors]}
        helperText={
          errors[name as keyof typeof errors]
            ? name === "MarsUsername"
              ? "Mars Username must be 8 characters or less"
              : "This field is required"
            : ""
        }
        inputProps={{
          "data-testid": `${name}-input`,
          maxLength: name === "MarsUsername" ? 8 : undefined,
        }}
      />
    </Box>
  );

  const renderSelectField = (
    name: keyof typeof formData,
    label: string,
    options: Array<{ value: string; label: string }>,
    required = false,
  ) => (
    <Box className={styles.formRow}>
      <InputLabel id={`${name}-label`} className={styles.label} data-testid={`${name}-label`}>
        {label} {required && <span style={{ color: "red" }}>*</span>}
      </InputLabel>
      <FormControl fullWidth size="small" error={errors[name as keyof typeof errors]}>
        <Select
          labelId={`${name}-label`}
          id={name}
          name={name}
          value={formData[name]}
          onChange={handleSelectChange}
          inputProps={{
            "data-testid": `${name}-select`,
          }}
        >
          <MenuItem value="">
            <em>Select {label}</em>
          </MenuItem>
          {options.map((option) => (
            <MenuItem
              key={option.value}
              value={option.value}
              data-testid={`${name}-option-${option.value}`}
            >
              {option.label}
            </MenuItem>
          ))}
        </Select>
        {errors[name as keyof typeof errors] && (
          <Typography variant="caption" color="error">
            This field is required
          </Typography>
        )}
      </FormControl>
    </Box>
  );

  const renderCheckboxField = (name: keyof typeof formData, label: string) => (
    <FormControlLabel
      control={
        <Checkbox
          name={name}
          checked={formData[name] as boolean}
          onChange={handleChange}
          color="primary"
          data-testid={`${name}-checkbox`}
        />
      }
      label={label}
      data-testid={`${name}-checkbox-label`}
    />
  );

  return (
    <div>
      <TopBarLayout breadcrumbItems={["Admin", "New User"]} />

      <Box sx={{ p: 3, width: "100%" }}>
        <Typography className={styles.title}>Fill New User Details</Typography>
        <Divider className={styles.divider} />

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              {renderTextField("UserLogin", "Login/Username", "text", true)}
              {renderTextField("UserFirstName", "First Name", "text", true)}
              {renderTextField("UserLastName", "Last Name", "text", true)}
              {renderTextField("UserEmail", "Email", "email", true)}
            </Grid>
            <Grid item xs={12} md={6}>
              {renderTextField("MarsUsername", "Mars Username")}
              {renderSelectField("UserType", "User Type", userTypes, true)}
              {renderSelectField(
                "RoleID",
                "Role",
                roles.map((role) => ({
                  value: role.RoleID,
                  label: role.RoleName,
                })),
                true,
              )}
              {renderSelectField(
                "ManagerID",
                "Manager",
                managers.map((manager) => ({
                  value: manager.UserID,
                  label: manager.UserFullName || manager.UserLogin,
                })),
              )}
            </Grid>
          </Grid>

          <Grid container spacing={3} mt={1}>
            <Grid item xs={4}>
              {renderCheckboxField("Active", "Active")}
            </Grid>
          </Grid>

          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-start",
              gap: 2,
              mt: 4,
            }}
          >
            <Button
              type="submit"
              variant="contained"
              sx={{
                backgroundColor: "#00008B",
                color: "#fff",
                fontWeight: 600,
                px: 4,
                "&:hover": { backgroundColor: "#000070" },
              }}
              data-testid="submit-button"
            >
              Save
            </Button>
            <Button
              variant="outlined"
              onClick={() => navigate("/user-list")}
              sx={{
                color: "#00008B",
                borderColor: "#00008B",
                fontWeight: 600,
                px: 4,
                "&:hover": {
                  backgroundColor: "#f0f0f0",
                  borderColor: "#00008B",
                },
              }}
              data-testid="cancel-button"
            >
              Cancel
            </Button>
          </Box>
        </form>
      </Box>
    </div>
  );
};

export default NewUser;
