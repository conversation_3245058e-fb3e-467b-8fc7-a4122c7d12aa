import { describe, it, vi, beforeEach, expect } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import store from '../../store';
import * as userActions from '../../services/action/user.action';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import UserList from '../admin/UsersList';
import { MemoryRouter } from 'react-router-dom';

// Mock toast notifications
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

// Mock components
vi.mock('../../components/TopBarLayout', () => ({
  default: ({ children, onSearchChange, searchValue }: any) => (
    <div>
      <input 
        data-testid="search-input"
        value={searchValue}
        onChange={(e) => onSearchChange(e.target.value)}
        placeholder="search"
      />
      {children}
    </div>
  )
}));

vi.mock('../../components/TableComponent', () => ({
  default: ({ columns, rows, onEdit, onDelete }: any) => (
    <div>
      <table>
        <tbody>
          {rows.map((row: any, index: number) => (
            <tr key={index}>
              <td>{row.UserLogin}</td>
              <td>
              <button data-testid="edit-button" onClick={() => onEdit(row)}>
                  Edit
                </button>
                <button data-testid="delete-button" onClick={() => onDelete(row)}>
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}));

// Mock react-router-dom's useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const renderWithProviders = (ui: React.ReactElement) => {
  return render(
    <Provider store={store}>
      <ThemeProvider theme={createTheme()}>
        <MemoryRouter>{ui}</MemoryRouter>
      </ThemeProvider>
    </Provider>
  );
};

describe('UserList Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    store.dispatch({ type: 'RESET' }); // Reset store state before each test
  });

  it('renders layout with Add New button', () => {
    renderWithProviders(<UserList />);
    expect(screen.getByText(/Add New/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument();
  });

  it('calls getAllRoles and getAllManagers on mount', async () => {
    const getAllRolesSpy = vi.spyOn(userActions, 'getAllRoles').mockImplementation(() => ({
      type: 'mockGetAllRoles'
    }));
    
    const getAllManagersSpy = vi.spyOn(userActions, 'getAllManagers').mockImplementation(() => ({
      type: 'mockGetAllManagers'
    }));

    renderWithProviders(<UserList />);

    await waitFor(() => {
      expect(getAllRolesSpy).toHaveBeenCalled();
      expect(getAllManagersSpy).toHaveBeenCalled();
    });
  });

  it('calls getUsers with correct parameters on mount', async () => {
    const getUsersSpy = vi.spyOn(userActions, 'getUsers').mockImplementation(() => ({
      type: 'mockGetUsers'
    }));

    renderWithProviders(<UserList />);

    await waitFor(() => {
      expect(getUsersSpy).toHaveBeenCalledWith({
        limit: 20,
        page: 1,
        active: undefined,
        search_query: ''
      });
    });
  });

  it('handles search input changes', async () => {
    const getUsersSpy = vi.spyOn(userActions, 'getUsers').mockImplementation(() => ({
      type: 'mockGetUsers'
    }));
    
    renderWithProviders(<UserList />);
    
    const searchInput = screen.getByTestId('search-input');
    fireEvent.change(searchInput, { target: { value: 'test' } });
    
    await waitFor(() => {
      expect(getUsersSpy).toHaveBeenCalledWith({
        limit: 20,
        page: 1,
        active: undefined,
        search_query: 'test'
      });
    });
  });

  it('navigates to new user page when Add New is clicked', () => {
    renderWithProviders(<UserList />);
    fireEvent.click(screen.getByText('Add New'));
    
    expect(mockNavigate).toHaveBeenCalledWith('/new-user');
  });
});