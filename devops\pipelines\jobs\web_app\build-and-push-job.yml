parameters:
- name: environmentName
  type: string
- name: registryServiceConnection
  type: string
- name: registryUri
  type: string
- name: imageName
  type: string
- name: tags
  type: string
- name: dockerfilePath
  type: string
- name: azureSubscription
  type: string
- name: appName
  type: string
- name: appSettings
  type: string
- name: appConfiguration
  type: string
- name: arguments
  type: string
  default: ''
  
jobs:
- job: build_image
  displayName: Build application image
  steps:
  - checkout: self
  - template: ../../tasks/docker/build.yml
    parameters:
      repository: ${{parameters.imageName}}
      dockerfilePath: ${{parameters.dockerfilePath}}
      registryServiceConnection: ${{parameters.registryServiceConnection}}
      tags: ${{parameters.tags}}
      arguments: ${{parameters.arguments}}
  - template: ../../tasks/docker/push.yml
    parameters:
      registryServiceConnection: ${{parameters.registryServiceConnection}}
      repository: ${{parameters.imageName}}
      tags: ${{parameters.tags}}
- deployment: ${{parameters.environmentName}}_deploy
  dependsOn: build_image
  displayName: Deploy Web App
  environment: ${{parameters.environmentName}}
  strategy:
    runOnce:
      deploy:
        steps:
        - template: ../../tasks/web_app/deploy-container-task.yml
          parameters:
            azureSubscription: ${{parameters.azureSubscription}}
            appName: ${{parameters.appName}}
            container: ${{parameters.registryUri}}/${{parameters.imageName}}:${{parameters.tags}}
            appSettings: ${{parameters.appSettings}}
            configurationStrings: ${{parameters.appConfiguration}}
