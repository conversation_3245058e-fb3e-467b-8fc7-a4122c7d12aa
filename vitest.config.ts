//eslint-disable-next-line
import { defineConfig, configDefaults } from "vitest/config";

export default defineConfig({
  test: {
    environment: "jsdom",
    globals: true,
    setupFiles: "src/setupTests.ts",
    reporters: ["json", "verbose"],
    outputFile: "./test-output.json",
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html"],
      all: true,
      include: ["src/**/*"],
      exclude: [...configDefaults.coverage.exclude!, "src/api/types/**", "src/vite-env.d.ts"],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 80,
        statements: 80,
      },
    },
  },
});
