import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, within } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import UserModal from '../UserModal'
import type { RootState } from '../../store'

// Mock MUI components to simplify testing
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Modal: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  }
})

// Mock the admin slice state
const mockAdminState = {
  roles: [
    { RoleID: '1', RoleName: 'Admin' },
    { RoleID: '2', RoleName: 'User' },
  ],
  managers: [
    { UserID: '101', UserLogin: 'manager1', UserFullName: 'John Manager' },
    { UserID: '102', UserLogin: 'manager2' }, // No full name case
  ],
  status: 'idle',
  error: null,
}

// Create a mock store with our mock state
const createMockStore = (preloadedState?: Partial<RootState>) => {
  return configureStore({
    reducer: {
      admin: (state = mockAdminState) => state, // Simple reducer that returns mock state
    },
    preloadedState,
  })
}

describe('UserModal Component', () => {
  const mockOnClose = vi.fn()
  const mockOnSave = vi.fn()

  const renderComponent = (props: Partial<React.ComponentProps<typeof UserModal>> = {}) => {
    const store = createMockStore()
    const defaultProps = {
      open: true,
      onClose: mockOnClose,
      onSave: mockOnSave,
      mode: 'add' as const,
    }

    return render(
      <Provider store={store}>
        <UserModal {...defaultProps} {...props} />
      </Provider>
    )
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render with "Add User" title in add mode', () => {
    renderComponent()
    expect(screen.getByText('Add User')).toBeInTheDocument()
  })

  it('should render with "Edit User" title in edit mode', () => {
    renderComponent({ mode: 'edit' })
    expect(screen.getByText('Edit User')).toBeInTheDocument()
  })

  it('should call onClose when cancel button is clicked', () => {
    renderComponent()
    fireEvent.click(screen.getByText('Cancel'))
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('should update form state when text fields are changed', () => {
    renderComponent()
    
    // Target the input elements within the TextField components
    const loginInput = screen.getByTestId('UserLogin-text').querySelector('input')!
    const firstNameInput = screen.getByTestId('UserFirstName-text').querySelector('input')!
    const emailInput = screen.getByTestId('UserEmail-text').querySelector('input')!
    
    fireEvent.change(loginInput, { target: { value: 'newuser' } })
    fireEvent.change(firstNameInput, { target: { value: 'New' } })
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    
    expect(loginInput).toHaveValue('newuser')
    expect(firstNameInput).toHaveValue('New')
    expect(emailInput).toHaveValue('<EMAIL>')
  })

  it('should call onSave with form data when save button is clicked', () => {
    renderComponent()
    
    // Target the input elements within the TextField components
    const loginInput = screen.getByTestId('UserLogin-text').querySelector('input')!
    const firstNameInput = screen.getByTestId('UserFirstName-text').querySelector('input')!
    const emailInput = screen.getByTestId('UserEmail-text').querySelector('input')!
    
    fireEvent.change(loginInput, { target: { value: 'testuser' } })
    fireEvent.change(firstNameInput, { target: { value: 'Test' } })
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    
    fireEvent.click(screen.getByText('Save'))

    expect(mockOnSave).toHaveBeenCalledWith({
      UserLogin: 'testuser',
      UserFirstName: 'Test',
      UserLastName: '',
      UserEmail: '<EMAIL>',
      MarsUsername: '',
      UserType: '',  
      ManagerID: '',
      IsLockedOut: false,
      Active: true,
      RoleID: '',
    })
  })

  it('should reset form when reopening in add mode', () => {
    const { rerender } = renderComponent({ open: false })
    
    const loginInput = screen.getByTestId('UserLogin-text').querySelector('input')!
    fireEvent.change(loginInput, { target: { value: 'temp' } })
    
    rerender(
      <Provider store={createMockStore()}>
        <UserModal open={true} onClose={mockOnClose} onSave={mockOnSave} mode="add" />
      </Provider>
    )
    
    expect(screen.getByTestId('UserLogin-text').querySelector('input')).toHaveValue('')
  })
})