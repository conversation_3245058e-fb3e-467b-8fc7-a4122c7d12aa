import React from "react";
import Breadcrumb from "./Breadcrump";
import SearchBar from "./Search";
import styles from "../styles/TopBarLayout.module.css";

interface TopBarLayoutProps {
  breadcrumbItems: string[];
  onSearchChange?: (value: string) => void;
}

const TopBarLayout: React.FC<TopBarLayoutProps> = ({
  breadcrumbItems,
  onSearchChange,
}) => {
  return (
    <div className={styles.container}>
      <Breadcrumb items={breadcrumbItems} />
      {onSearchChange && <SearchBar onSearchChange={onSearchChange} />}
    </div>
  );
};

export default TopBarLayout;
