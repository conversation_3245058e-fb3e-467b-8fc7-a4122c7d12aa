import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import ProductSearch from '../ProductSearch';
import productReducer from '../../../store/slice/productSlice';

// Mock the product service
vi.mock('../../../services/api/product.service', () => ({
  productService: {
    getListItems: vi.fn().mockResolvedValue({
      brands: [
        { value: 'pedigree', label: 'Pedigree' },
        { value: 'whiskas', label: 'Whiskas' },
      ]
    }),
    searchProducts: vi.fn().mockResolvedValue({
      data: [
        {
          PRDNO: 'PRD001',
          DESCP: 'Test Product',
          ItemID: 'ITM001',
          SkuName: 'TEST-SKU',
          AssociatedItem: 'ASC001',
          Brand: 'Pedigree',
        }
      ],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    }),
  },
}));

const createTestStore = () => {
  return configureStore({
    reducer: {
      product: productReducer,
    },
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('ProductSearch', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the product search page', () => {
    renderWithProviders(<ProductSearch />);
    
    expect(screen.getByText('Product Search')).toBeInTheDocument();
  });

  it('displays filter fields', () => {
    renderWithProviders(<ProductSearch />);
    
    expect(screen.getByLabelText('PRDNO')).toBeInTheDocument();
    expect(screen.getByLabelText('DESCP')).toBeInTheDocument();
    expect(screen.getByLabelText('REUPC')).toBeInTheDocument();
    expect(screen.getByLabelText('Brand')).toBeInTheDocument();
    expect(screen.getByLabelText('SalesDesc')).toBeInTheDocument();
    expect(screen.getByLabelText('AssocItem')).toBeInTheDocument();
  });

  it('has apply and reset buttons', () => {
    renderWithProviders(<ProductSearch />);
    
    expect(screen.getByText('Apply')).toBeInTheDocument();
    expect(screen.getByText('Reset')).toBeInTheDocument();
  });

  it('shows table only after applying filters', async () => {
    renderWithProviders(<ProductSearch />);
    
    // Table should not be visible initially
    expect(screen.queryByRole('table')).not.toBeInTheDocument();
    
    // Fill in a filter and apply
    const prdnoInput = screen.getByLabelText('PRDNO');
    fireEvent.change(prdnoInput, { target: { value: 'PRD001' } });
    
    const applyButton = screen.getByText('Apply');
    fireEvent.click(applyButton);
    
    // Table should now be visible
    await waitFor(() => {
      expect(screen.getByRole('table')).toBeInTheDocument();
    });
  });

  it('resets filters when reset button is clicked', () => {
    renderWithProviders(<ProductSearch />);
    
    // Fill in a filter
    const prdnoInput = screen.getByLabelText('PRDNO');
    fireEvent.change(prdnoInput, { target: { value: 'PRD001' } });
    
    expect(prdnoInput).toHaveValue('PRD001');
    
    // Click reset
    const resetButton = screen.getByText('Reset');
    fireEvent.click(resetButton);
    
    // Field should be cleared
    expect(prdnoInput).toHaveValue('');
  });
});
