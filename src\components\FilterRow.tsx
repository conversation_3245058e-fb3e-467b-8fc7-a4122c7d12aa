import React from "react";
import { <PERSON>, Button, TextField, Stack, MenuItem, Chip } from "@mui/material";

interface FilterOption {
  value: string;
  label: string;
}

interface FilterConfig {
  name: string;
  label: string;
  type: "text" | "select" | "multiselect" | "custom";
  value: string | string[];
  onChange: (value: string | string[]) => void;
  options?: FilterOption[];
  component?: React.ReactNode;
}

interface FilterRowProps {
  filters: FilterConfig[];
  onReset?: () => void;
  onApply?: () => void;
  showActionButtons?: boolean;
}

const FilterRow: React.FC<FilterRowProps> = ({
  filters,
  onReset,
  onApply,
  showActionButtons = true,
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        pt: 1,
        gap: 2,
        px: 2,
        flexWrap: "wrap",
      }}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 2, flexWrap: "wrap" }}>
        {filters.map((filter) => {
          if (filter.type === "custom" && filter.component) {
            return <Box key={filter.name}>{filter.component}</Box>;
          }

          if (filter.type === "text") {
            return (
              <TextField
                key={filter.name}
                label={filter.label}
                value={filter.value}
                onChange={(e) => filter.onChange(e.target.value)}
                size="small"
                sx={{ minWidth: 180 }}
                variant="outlined"
              />
            );
          }

          if (filter.type === "select") {
            return (
              <TextField
                key={filter.name}
                select
                label={filter.label}
                value={filter.value}
                onChange={(e) => filter.onChange(e.target.value)}
                size="small"
                sx={{ minWidth: 180 }}
                variant="outlined"
                SelectProps={{
                  displayEmpty: true,
                }}
              >
                {filter.options?.length ? (
                  filter.options.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem disabled value="">
                    No options available
                  </MenuItem>
                )}
              </TextField>
            );
          }

          if (filter.type === "multiselect") {
            return (
              <TextField
                key={filter.name}
                select
                SelectProps={{
                  multiple: true,
                  value: filter.value,
                  onChange: (e) => filter.onChange(e.target.value as string[]),
                  renderValue: (selected) => (
                    <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                      {(selected as string[]).map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  ),
                }}
                label={filter.label}
                size="small"
                sx={{ minWidth: 180 }}
                variant="outlined"
              >
                {filter.options?.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </TextField>
            );
          }

          return null;
        })}

        {showActionButtons && (
          <Stack direction="row" spacing={2}>
            {onApply && (
              <Button variant="contained" onClick={onApply} size="small">
                Apply
              </Button>
            )}
            {onReset && (
              <Button variant="outlined" onClick={onReset} size="small">
                Reset
              </Button>
            )}
          </Stack>
        )}
      </Box>
    </Box>
  );
};

export default FilterRow;
