import { createAsyncThunk } from "@reduxjs/toolkit";
import AttributeService from "../api/attribute.service";

interface AttributeParams {
  limit?: number;
  page?: number;
  list_name?: string;
  item_name?: string;
  item_value?: string;
  parent_list_name?: string;
  parent_item_value?: string;
  search_query?: string;
}

interface AttributeItem {
  RecID?: number;
  ListName: string;
  ItemName: string;
  ItemValue: string;
  ItemDescription?: string;
  ItemOrder?: number;
  ParentListName?: string;
  ParentItemValue?: string;
}

export const getAttributeListItems = createAsyncThunk(
  "attribute/getAttributeListItems",
  async (params: AttributeParams) => {
    const response = await AttributeService.getAttributeListItems(
      params.limit || 20,
      params.page || 1,
      params.list_name || '',
      params.search_query || ''
    );
    return response;
  }
);

export const getAllListNames = createAsyncThunk(
  "attribute/getAllListNames",
  async () => {
    const response = await AttributeService.getAllListNames();
    return response.data;
  }
);

export const createAttributeListItem = createAsyncThunk(
  "attribute/createAttributeListItem",
  async (data: AttributeItem) => {
    const response = await AttributeService.createAttributeListItem(data);
    return response.data;
  }
);

export const updateAttributeListItem = createAsyncThunk(
  "attribute/updateAttributeListItem",
  async ({ id, data }: { id: number; data: AttributeItem }) => {
    const response = await AttributeService.updateAttributeListItem(id, data);
    return response.data;
  }
);

export const patchAttributeListItem = createAsyncThunk(
  "attribute/patchAttributeListItem",
  async ({ id, data }: { id: number; data: Partial<AttributeItem> }) => {
    const response = await AttributeService.patchAttributeListItem(id, data);
    return response.data;
  }
);

export const deleteAttributeListItem = createAsyncThunk(
  "attribute/deleteAttributeListItem",
  async (id: number) => {
    await AttributeService.deleteAttributeListItem(id);
    return id;
  }
);