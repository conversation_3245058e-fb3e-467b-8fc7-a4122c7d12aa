import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import TopBarLayout from '../TopBarLayout'

// Mock the child components
vi.mock('../Breadcrump', () => ({ 
  default: ({ items }: { items: string[] }) => (
    <nav>
      {items.map((item, index) => (
        <span key={index}>{item}</span>
      ))}
    </nav>
  )
}))

vi.mock('../Search', () => ({
  default: ({ onSearchChange }: { onSearchChange?: (value: string) => void }) => (
    <input
      type="text"
      placeholder="Search..."
      onChange={(e) => onSearchChange?.(e.target.value)}
    />
  ),
}))

describe('TopBarLayout Component', () => {
  const breadcrumbItems = ['Home', 'Dashboard', 'Reports']

  it('renders breadcrumb items', () => {
    render(<TopBarLayout breadcrumbItems={breadcrumbItems} />)
    breadcrumbItems.forEach((item) => {
      expect(screen.getByText(item)).toBeInTheDocument()
    })
  })

  it('renders search bar', () => {
    render(<TopBarLayout breadcrumbItems={breadcrumbItems} />)
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument()
  })

  it('calls onSearchChange when input changes', () => {
    const mockSearchChange = vi.fn()
    render(
      <TopBarLayout
        breadcrumbItems={breadcrumbItems}
        onSearchChange={mockSearchChange}
      />
    )

    const input = screen.getByPlaceholderText('Search...')
    fireEvent.change(input, { target: { value: 'Report 2023' } })

    expect(mockSearchChange).toHaveBeenCalledWith('Report 2023')
  })
})