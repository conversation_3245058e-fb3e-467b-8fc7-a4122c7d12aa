// src/components/SearchBar.tsx
import React from "react";
import { TextField, InputAdornment } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import styles from "../styles/Search.module.css";

interface SearchBarProps {
  onSearchChange?: (value: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearchChange }) => {
  return (
    <TextField
      variant="outlined"
      placeholder="Search"
      size="small"
      onChange={(e) => onSearchChange?.(e.target.value)}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon className={styles.icon} />
          </InputAdornment>
        ),
      }}
      className={styles.searchBar}
    />
  );
};

export default SearchBar;
